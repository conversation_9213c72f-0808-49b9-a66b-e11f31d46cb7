# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[('assets', 'assets')],
    hiddenimports=['encodings', 'numpy', 'pandas', '_bootlocale', 'boto3', 'botocore', 'botocore.vendored', 'jmespath', 'urllib3'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='SACorp-SAASbackofficev1_13',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=['IconeMACOS.icns'],
)
app = BUNDLE(
    exe,
    name='SACorp-SAASbackofficev1_13.app',
    icon='IconeMACOS.icns',
    bundle_identifier=None,
)
