"""
Widget pour la gestion des groupes de campagnes.
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
                            QTableView, QMessageBox)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QStandardItemModel, QStandardItem, QPixmap
import os
import sys

from .group_campaign_dialogs import AddGroupCampaignDialog, ModifyGroupCampaignDialog

class GroupCampaignTableWidget(QWidget):
    def __init__(self, font_manager, controller):
        super().__init__()

        self.controller = controller
        self.font_manager = font_manager
        
        # Création du modèle avec colonnes
        self.model = QStandardItemModel(0, 6)  # 0 lignes, 6 colonnes
        self.model.setHorizontalHeaderLabels(["ID", "Date de création", "Nom du groupe",
                                              "nb campagnes", "Budget margé", "Budget non margé"])

        self.locked_columns = [0, 1, 3, 4, 5]  # Colonnes verrouillées (ID, Date de création, Nombre de campagnes)
        self.modified_rows = set()  # Stocke les lignes modifiées

        # Création du tableau
        self.table = QTableView()
        self.table.setSortingEnabled(True)  # Active le tri au clic
        self.table.setModel(self.model)
        self.table.setFixedSize(800, 150)

        # Activer la sélection de toute la ligne
        self.table.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.table.setSelectionMode(QTableView.SelectionMode.SingleSelection)
        self.table.setStyleSheet("""
            QTableView::item:selected {
                background-color: #476AFF;
                color: white;
            }
        """)

        column_widths = {
            0: 50,   # ID
            1: 100,  # Date de création
            2: 250,  # Nom du groupe
            3: 90,   # Nombre de campagnes
            4: 120,  # Budget margé
            5: 120   # Budget non margé
        }
        for col_index, width in column_widths.items():
            self.table.setColumnWidth(col_index, width)

        # Connecter le signal pour détecter les modifications
        self.model.dataChanged.connect(self.highlight_modified_row)

        # Layout principal
        self.layout = QVBoxLayout()
        self.layout.addWidget(self.table)

        # Boutons pour les actions sur les groupes de campagnes
        self.group_button_layout = QHBoxLayout()
        self.group_button_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)
        
        self.add_group_button = QPushButton("Créer")
        self.add_group_button.setEnabled(False)
        self.add_group_button.setMaximumWidth(100)
        
        self.delete_group_button = QPushButton("Supprimer")
        self.delete_group_button.setEnabled(False)
        self.delete_group_button.setMaximumWidth(100)
        
        self.modify_group_button = QPushButton("Modifier")
        self.modify_group_button.setEnabled(False)
        self.modify_group_button.setMaximumWidth(100)
        
        self.group_button_layout.addWidget(self.add_group_button)
        self.group_button_layout.addWidget(self.modify_group_button)
        self.group_button_layout.addWidget(self.delete_group_button)
        
        self.layout.addLayout(self.group_button_layout)
        self.setLayout(self.layout)

    def highlight_modified_row(self, top_left, bottom_right):
        """Met en évidence les lignes modifiées."""
        for row in range(top_left.row(), bottom_right.row() + 1):
            self.modified_rows.add(row)
            
            # Modifier la couleur si la ligne est sélectionnée
            self.table.setStyleSheet(f"""
                QTableView::item:selected {{
                    background-color: #FF8C00;  /* Orange foncé uniquement pour les lignes modifiées */
                    color: white;
                }}
            """)
            
    def get_table(self):
        """Retourne le QTableView pour être ajouté ailleurs"""
        return self.table

    def update_data(self, data):
        """Met à jour les données du tableau en gardant les colonnes verrouillées"""
        self.model.removeRows(0, self.model.rowCount())  # Supprime les anciennes lignes

        for row, values in enumerate(data):
            for col, value in enumerate(values):
                item = QStandardItem(str(value))
                if isinstance(value, int):  # Permet un tri correct des nombres
                    item.setData(value, Qt.ItemDataRole.EditRole)

                # Désactiver l'édition pour les colonnes verrouillées
                if col in self.locked_columns:
                    item.setFlags(item.flags() & ~Qt.ItemFlag.ItemIsEditable)

                self.model.setItem(row, col, item)

    def open_add_group_window(self, account_id_selected, account_name_selected, project_id_selected, project_name_selected, controller):
        """Ouvre une nouvelle fenêtre pour ajouter un groupe de campagne"""
        dialog = AddGroupCampaignDialog(account_id_selected, account_name_selected, project_id_selected, project_name_selected, controller, self.font_manager)
        dialog.exec()  # Affiche la boîte de dialogue en mode bloquant

    def open_modify_group_window(self, account_id_selected, account_name_selected, project_id_selected, project_name_selected, controller):
        """Ouvre une nouvelle fenêtre pour modifier un groupe de campagne"""
        selection_model = self.table.selectionModel()
        selected_rows = selection_model.selectedRows()
        if not selected_rows:
            QMessageBox.warning(self, "Erreur", "Veuillez sélectionner un groupe de campagne.")
            return

        selected_index = selected_rows[0]
        groupcampaing_id = int(self.model.item(selected_index.row(), 0).text())
        groupcampaing_name = self.model.item(selected_index.row(), 2).text()
        dialog = ModifyGroupCampaignDialog(account_id_selected, account_name_selected, project_id_selected, project_name_selected, controller, self.font_manager, groupcampaing_id, groupcampaing_name)
        dialog.exec()  # Affiche la boîte de dialogue en mode bloquant

    def del_groupcampaign(self, project_id_selected):
        """Supprime un groupe de campagne"""
        selection_model = self.table.selectionModel()
        selected_rows = selection_model.selectedRows()
        if not selected_rows:
            msg_box = QMessageBox()
            msg_box.setWindowTitle("Erreur")
            msg_box.setText("Veuillez sélectionner un groupe de campagne.")
            
            # Définir une icône personnalisée
            if getattr(sys, 'frozen', False):
                base_path = sys._MEIPASS
                assets_path = os.path.join(base_path, "assets", "icons")
            else:
                assets_path = os.path.abspath("assets/icons/")
                
            icon_path = os.path.join(assets_path, "custom_error.png")
            if os.path.exists(icon_path):
                msg_box.setIconPixmap(QPixmap(icon_path).scaled(48, 48, Qt.AspectRatioMode.KeepAspectRatio))
            msg_box.exec()
            return

        selected_index = selected_rows[0]
        groupcampaign_id = int(self.model.item(selected_index.row(), 0).text())

        # Confirmation de suppression
        confirmation = QMessageBox.question(
            self,
            "Confirmation de suppression",
            "Êtes-vous sûr de vouloir supprimer ce groupe de campagne ?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if confirmation != QMessageBox.StandardButton.Yes:
            return

        success, message = self.controller.del_groupcampaign_to_xano(project_id_selected, groupcampaign_id)
        
        if success:
            msg_box = QMessageBox()
            msg_box.setWindowTitle("Succès")
            msg_box.setText("Groupe de campagne supprimé avec succès.")
            
            # Définir une icône personnalisée
            if getattr(sys, 'frozen', False):
                base_path = sys._MEIPASS
                assets_path = os.path.join(base_path, "assets", "icons")
            else:
                assets_path = os.path.abspath("assets/icons/")
                
            icon_path = os.path.join(assets_path, "custom_success.png")
            if os.path.exists(icon_path):
                msg_box.setIconPixmap(QPixmap(icon_path).scaled(48, 48, Qt.AspectRatioMode.KeepAspectRatio))
            msg_box.exec()
        else:
            QMessageBox.warning(self, "Erreur", f"Échec de la suppression du groupe de campagne : {message}")
            
        self.controller.update_groupcampaigns_options()
