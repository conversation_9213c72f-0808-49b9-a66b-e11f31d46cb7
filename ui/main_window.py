"""
Fenêtre principale de l'application.
"""

from PyQt6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QMessageBox
from PyQt6.QtCore import Qt

from .account_widget import AccountWidget
from .project_widget import ProjectWidget
from .group_campaign_table import GroupCampaignTableWidget
from .campaigns_table import CampaignsTableWidget
from .campaign_filters import CampaignFiltersWidget

class MainWindow(QWidget):
    def __init__(self):
        super().__init__()
        from controller import DataController  # Import ici pour éviter les imports circulaires
        self.controller = DataController(self)  # Contrôleur pour gérer les requêtes
        
        from tools.font_manager import FontManager
        self.font_manager = FontManager()
        
        self.init_ui()

    def init_ui(self):
        self.setWindowTitle("SA Corp - Saas Backoffice")
        self.setGeometry(100, 100, 1500, 700)
        self.setFont(self.font_manager.get_font("regular", 12))

        layout = QVBoxLayout()

        # Barre de navigation supérieure
        topnav_layout = QHBoxLayout()
        topnav_layout.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignTop)
        topnav_left_layout = QHBoxLayout()
        topnav_left_layout.setAlignment(Qt.AlignmentFlag.AlignTop)  # S'assurer que tout est aligné en haut
        
        # Widgets pour les comptes et projets
        self.account_widget = AccountWidget(self.font_manager)
        topnav_left_layout.addWidget(self.account_widget.get_layout())
        self.project_widget = ProjectWidget(self.font_manager)
        topnav_left_layout.addWidget(self.project_widget.get_layout())
        topnav_left_layout.addSpacing(20)  # Ajoute un peu d'espace entre les éléments
        topnav_layout.addLayout(topnav_left_layout)

        # Tableau des groupes de campagnes
        self.group_campaign_table = GroupCampaignTableWidget(self.font_manager, self.controller)
        topnav_layout.addWidget(self.group_campaign_table)

        layout.addLayout(topnav_layout)

        # Tableau des campagnes et filtres
        self.campaigns_table = CampaignsTableWidget(self.font_manager, self.controller)
        self.campaignsfilter = CampaignFiltersWidget(self.campaigns_table)
        layout.addWidget(self.campaignsfilter)
        layout.addWidget(self.campaigns_table)

        self.setLayout(layout)

        # Charger les premiers choix
        self.controller.load_initial_options()

        # Connecter les événements
        self._connect_events()

    def _connect_events(self):
        """Connecte tous les événements de l'interface utilisateur."""
        # Événements des comptes
        self.account_widget.accounts_dropdown.currentIndexChanged.connect(
            lambda index: self.controller.update_projects_options() if self.account_widget.accounts_dropdown.count() > 0 else None
        )
        self.project_widget.projects_dropdown.currentIndexChanged.connect(self.controller.update_groupcampaigns_options)

        self.group_campaign_table.get_table().selectionModel().selectionChanged.connect(
            self.controller.update_campaigns_options
        )
        self.group_campaign_table.get_table().selectionModel().selectionChanged.connect(
            lambda: self.campaignsfilter.update_filters_from_table(self.campaigns_table.model)
        )

        # Événements des boutons de compte
        self.account_widget.add_account_button.clicked.connect(
            lambda: self.account_widget.open_add_account_window(self.controller)
        )
        self.account_widget.user_account_button.clicked.connect(
            lambda: self.account_widget.open_user_account_window(self.account_id_selected, self.account_name_selected, self.controller)
        )

        # Événements des boutons de projet
        self.project_widget.add_project_button.clicked.connect(
            lambda: self.project_widget.open_add_project_window(self.account_id_selected, self.account_name_selected, self.controller)
        )
        
        # Événements des boutons de groupe de campagne
        self.group_campaign_table.add_group_button.clicked.connect(
            lambda: self.group_campaign_table.open_add_group_window(self.account_id_selected, self.account_name_selected, self.project_id_selected, self.project_name_selected, self.controller)
        )
        self.group_campaign_table.delete_group_button.clicked.connect(
            lambda: self.group_campaign_table.del_groupcampaign(self.project_id_selected)
        )
        self.group_campaign_table.modify_group_button.clicked.connect(
            lambda: self.group_campaign_table.open_modify_group_window(self.account_id_selected, self.account_name_selected, self.project_id_selected, self.project_name_selected, self.controller)
        )

        # Événements des boutons de campagne
        self.campaigns_table.create_button.clicked.connect(
            lambda: self.campaigns_table.open_add_campaign_window(self.account_id_selected, self.account_name_selected, self.project_id_selected, self.project_name_selected, self.group_id_selected, self.group_name_selected, self.controller)
        )
        self.campaigns_table.delete_button.clicked.connect(
            lambda: self.campaigns_table.del_campaign(self.group_id_selected)
        )
        self.campaigns_table.modify_button.clicked.connect(
            lambda: self.campaigns_table.open_modify_campaign_window(self.account_id_selected, self.account_name_selected, self.project_id_selected, self.project_name_selected, self.group_id_selected, self.group_name_selected, self.controller)
        )
        self.campaigns_table.view_button.clicked.connect(
            lambda: self.campaigns_table.open_view_campaign_window(self.account_id_selected, self.account_name_selected, self.project_id_selected, self.project_name_selected, self.group_id_selected, self.group_name_selected, self.controller)
        )

    def closeEvent(self, event):
        print("App fermée proprement")
        # Arrêter les timers, threads, sauvegarder l'état, etc.
        event.accept()

    def show_message(self, title, message):
        """Affiche un message à l'utilisateur."""
        QMessageBox.information(self, title, message)
