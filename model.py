import time
import requests
from datetime import datetime,timezone
import boto3
import os
from botocore.exceptions import NoCredentialsError


class DatabaseModel:
    def __init__(self):
        self.connection = self.initialize_connection()
        self.route_getaccounts = "https://x6ny-u01k-kxvn.p7.xano.io/api:2sk9qabc/account"
        self.route_getusers = "https://x6ny-u01k-kxvn.p7.xano.io/api:2sk9qabc/get_user"
        self.route_getprojects = "https://x6ny-u01k-kxvn.p7.xano.io/api:2sk9qabc/project"
        self.route_getgroupcampaigns = "https://x6ny-u01k-kxvn.p7.xano.io/api:2sk9qabc/groupcampaign"
        self.route_getcampaigns = "https://x6ny-u01k-kxvn.p7.xano.io/api:2sk9qabc/campaigns"
        self.route_getcampaignid = "https://x6ny-u01k-kxvn.p7.xano.io/api:2sk9qabc/get_campaignid"
        self.route_getbmaccounts = "https://x6ny-u01k-kxvn.p7.xano.io/api:2sk9qabc/get_adaccount"
        self.route_getallcampaigns = "https://x6ny-u01k-kxvn.p7.xano.io/api:2sk9qabc/get_allcampaigns"
        self.route_getallcampaigns_stats = "https://x6ny-u01k-kxvn.p7.xano.io/api:2sk9qabc/get_campaigns_and_stats"
        self.route_getinvoice = "https://x6ny-u01k-kxvn.p7.xano.io/api:2sk9qabc/get_invoice"


        

        self.route_postaccount = "https://x6ny-u01k-kxvn.p7.xano.io/api:2sk9qabc/post_account"
        self.route_postproject = "https://x6ny-u01k-kxvn.p7.xano.io/api:2sk9qabc/post_project"
        self.route_postcampaign = "https://x6ny-u01k-kxvn.p7.xano.io/api:2sk9qabc/post_campaigns"
        self.route_postinvoice = "https://x6ny-u01k-kxvn.p7.xano.io/api:2sk9qabc/post_invoice"
        self.route_deleteinvoice = "https://x6ny-u01k-kxvn.p7.xano.io/api:2sk9qabc/delete_invoice"
        self.route_editinvoice = "https://x6ny-u01k-kxvn.p7.xano.io/api:2sk9qabc/edit_invoice"

        self.route_postgroupcampaign = "https://x6ny-u01k-kxvn.p7.xano.io/api:2sk9qabc/post_campaigngroup"
        self.route_delgroupcampaign = "https://x6ny-u01k-kxvn.p7.xano.io/api:2sk9qabc/delete_campaigngroup"
        self.route_editgroupcampaign = "https://x6ny-u01k-kxvn.p7.xano.io/api:2sk9qabc/edit_campaigngroup"
        self.route_editcampaign = "https://x6ny-u01k-kxvn.p7.xano.io/api:2sk9qabc/edit_campaigns"



        ##AWS
        self.aws_access_key = AwsCredentialManager.get_access_key()
        self.aws_secret_key = AwsCredentialManager.get_secret_key()

        self.aws_invoice_bucket_name = "sacorp-invoice-bucket"
        self.aws_region = "eu-west-3"

        


    def initialize_connection(self):
        """Initialise la connexion à la base de données."""
        # Simuler l'initialisation de la connexion
        time.sleep(1)
        return "Connexion simulée"
    
    def fetch_accounts(self):
        """Récupère les comptes de la base de données."""
        
        params = {
            'token': TokenManager.get_token()
        }
        

        response = requests.get(self.route_getaccounts, params=params)
        # print( response.json() )
        accounts_data = []

        for account in response.json():
            account_logo = account.get('account_logo', {})  # Vérifie si 'account_logo' existe
            account_logo_url = account_logo['url'] if account_logo and 'url' in account_logo else ''

            accounts_data.append((account['id'], account['account_name'], account_logo_url))

        return accounts_data
    
    def fetch_user(self,account_id):
        """Récupère les user du compte de la base de données."""
        
        data = {
            "account_id": account_id,
            'token': TokenManager.get_token()
        }

        response = requests.get(self.route_getusers,params=data)
        # print( response.json() )
        user_data = []

        for user in response.json():
            user_id = user.get('id')  
            user_email = user.get('email') 
            user_name = user.get('name')  
            user_firstname = user.get('First_name')  
            user_company = user.get('Company')  
            user_role = user.get('Role')  

            user_data.append((user_id, user_email, user_name, user_firstname,user_company,user_role))
        # print(user_data)
        return user_data



    def fetch_projects(self, account_id):
        """Récupère les comptes de la base de données."""
        data = {
            "account_id": account_id,
            'token': TokenManager.get_token()
        }
        response = requests.get(self.route_getprojects, params=data)
        # print( response.json() )
        projects_data = []

        for project in response.json():
            project_logo = project.get('logo', {})
            project_logo_url = project_logo['url'] if project_logo and 'url' in project_logo else ''
            projects_data.append((project['id'], project['Project_Name'],project_logo_url))
            

        return projects_data
    
    def fetch_groupcampaigns(self, project_id, account_id):
        """Récupère les comptes de la base de données."""
        data = {
            "account_id": account_id,
            "project_id": project_id,
            'token': TokenManager.get_token()
        }
        response = requests.get(self.route_getgroupcampaigns, params=data)
        # print( response.json() )
        groupscampaigns_data = []

        for groupcampaigns in response.json():
            groupscampaigns_data.append([
                groupcampaigns['id'],# ID du groupe de campagnes
                datetime.fromtimestamp(groupcampaigns['created_at'] / 1000, tz=timezone.utc).strftime('%Y-%m-%d') , # Date de creation
                groupcampaigns['groupname'],# Nom du groupe
                groupcampaigns['nb_campain'],# Nombre de campagnes
                groupcampaigns['total_budget_margin'],  # Budget total avec marge
                groupcampaigns['total_budget_nomargin'] # Budget total sans marge
            ])
        return groupscampaigns_data
       

    def fetch_campaigns(self, project_id, account_id,selected_groupcampaign_id):
        data = {
            "account_id": account_id,
            "project_id": project_id,
            "campaigngroup_id": selected_groupcampaign_id,
            'token': TokenManager.get_token()
        }
        # print("Debut du fetch des campaign")
        # print("data envoyé a xano: ", data)

        response = requests.get(self.route_getcampaigns, params=data)
        # print("reponse statut : ", response)
        # print("reponse : ", response.json())
        campaigns_data = []

        for campaigns in response.json():
            campaigns_data.append([
                campaigns['id'],  # ID
                # datetime.fromtimestamp(campaigns['created_at'] / 1000, tz=timezone.utc).strftime('%Y-%m-%d'),  # created_at
                # campaigns['campaigngroup_id'],  # campaigngroup_id
                campaigns['campaign_name'],  # campaign_name
                campaigns['leveler'],  # leveler
                campaigns['objective'],  # objective
                campaigns['total_budget'],  # total_budget
                campaigns['nomarginbudget'],  # nomarginbudget
                campaigns['state'],  # state
                campaigns['start_date'],  # start_date (format: 'YYYY-MM-DD' attendu)
                campaigns['end_date'],  # end_date (format: 'YYYY-MM-DD' attendu)
                campaigns['public_id'],  # ID_Campaign
                campaigns['campaign_public_name'],  # campaign_public_name
                campaigns['ID_adminadaccount'],  # ID_adminadaccount
                campaigns['ID_AdAccount'],  # ID_AdAccount
                campaigns['ID_Campaign']  # ID_Campaign
                
            ])

        return campaigns_data

        return

    def fetch_bmaccounts(self):

        """Récupère les comptes de la base de données, séparés en comptes Admin et comptes Ad."""
        
        data = {
            'token': TokenManager.get_token()
        }

        response = requests.get(self.route_getbmaccounts, params=data)
        data = response.json()
 
        admin_accounts_data = []
        ad_accounts_data = []
       
 
        for account in data:
            entry = {
                "id": account.get("id"),
                "created_at": datetime.fromtimestamp(account.get("created_at", 0) / 1000, tz=timezone.utc).strftime('%Y-%m-%d'),
                "leveler": account.get("leveler"),
                "type": account.get("type"),
                "name": account.get("name"),
                "id_account": account.get("id_account"),
            }
 
            if account.get("type") == "AdminAccount":
                admin_accounts_data.append(entry)
            elif account.get("type") == "AdAcount":
                ad_accounts_data.append(entry)



        return admin_accounts_data, ad_accounts_data

    def fetch_campaign_id(self,campaign_id):
        data = {
            "campaign_id": int(campaign_id),
            'token': TokenManager.get_token()
        }
        print("data envoyé a xano: ", data)
        response = requests.get(self.route_getcampaignid, params=data)
        campaign_data = []

        for campaign in response.json():
            campaign_data.append({
                'id': campaign.get('id'),
                'created_at': campaign.get('created_at'),
                'campaigngroup_id': campaign.get('campaigngroup_id'),
                'campaign_name': campaign.get('campaign_name'),
                'leveler': campaign.get('leveler'),
                'objective': campaign.get('objective'),
                'total_budget': campaign.get('total_budget'),
                'state': campaign.get('state'),
                'nomarginbudget': campaign.get('nomarginbudget'),
                'start_date': campaign.get('start_date'),
                'end_date': campaign.get('end_date'),
                'ID_adminadaccount': campaign.get('ID_adminadaccount'),
                'ID_AdAccount': campaign.get('ID_AdAccount'),
                'ID_Campaign': campaign.get('ID_Campaign'),
                'public_id' : campaign.get('public_id'),  # ID_Campaign
                'campaign_public_name' : campaign.get('campaign_public_name')
            })
        

        return campaign_data


    def fetch_allcampaigns(self):
        data = {
            'token': TokenManager.get_token()
        }
        # print("Debut du fetch des campaign")
        # print("data envoyé a xano: ", data)

        response = requests.get(self.route_getallcampaigns_stats, params=data)
        # print("reponse statut : ", response)
        # print("reponse : ", response.json())
        campaigns_data = []
        campaign_columns_index = {
            "id": 0,
            "campaign_name": 1,
            "leveler": 2,
            "objective": 3, 
            "kpi": 4,
            "total_budget": 5,
            "nomarginbudget": 6,
            "state": 7,
            "start_date": 8,
            "end_date": 9,
            "public_id": 10,
            "campaign_public_name": 11,
            "ID_adminadaccount": 12,
            "ID_AdAccount": 13, 
            "ID_Campaign": 14,
            "spent_budget": 15,
            "impression": 16,
            "view": 17,
            "clic": 18,
            "reach": 19, 
            "purchase": 20,
            "purchase_value": 21,
            "add_to_cart": 22,
            "add_to_cart_value": 23,
            "kpi_with_margin": 24,
            "kpi_no_margin": 25,
            "spent_budget_margin": 26,
            "objective_target": 27,

            # ➕ KPIs margés
            "cpc_margin": 28,
            "cpm_margin": 29,
            "cpv_margin": 30,
            "cpa_margin": 31,

            # ➖ KPIs non margés
            "cpc_no_margin": 32,
            "cpm_no_margin": 33,
            "cpv_no_margin": 34,
            "cpa_no_margin": 35,

            # ➕ Autres KPIs
            "repetition": 36,
            "ctr": 37,
            "vtr": 38,

            # ➕ ROAS margé / non margé
            "roas_margin": 39,
            "roas_no_margin": 40,

            # Finnciaire
            "marge" : 41,
            "benefices_total" : 42
        }

        # Définir les correspondances objectif -> KPI pour tous les leviers
        objective_to_kpi = {
            # Meta/Facebook/Instagram
            "Trafic": "CPC",
            "Notoriété": "CPM",
            "Vues de vidéo": "CPV",
            "Vente": "CPA",
            "Interactions": "CPI",
            "Visites de profil": "CPC",
            
            # Snapchat
            # (Déjà couvert par les objectifs Meta)
            
            # TikTok
            "Followers": "CPF",
            
            # Google Ads
            "Search": "CPC",
            "Display": "CPM",
            
            # YouTube
            "Preroll": "CPV",
            "Bumper": "CPM",
            "Discovery": "CPV",
            "YT for Action": "CPC",
            
            # Spotify
            "Streams": "CPS",
        }
       
        
        for campaigns in response.json():
            kpi = objective_to_kpi.get(campaigns['objective'], "N/A")

            # Calculate KPI values with and without margin
            spent_budget = float(campaigns.get('data', {}).get('spent_budget', 0) or 0)
            
            # Get relevant metrics based on KPI type
            if kpi == "CPM":
                metric = float(campaigns.get('data', {}).get('impression', 0) or 0) / 1000  # Convert to thousands
            elif kpi == "CPV":
                metric = float(campaigns.get('data', {}).get('view', 0) or 0)
            elif kpi in ["CPC"]:
                metric = float(campaigns.get('data', {}).get('clic', 0) or 0)
            elif kpi == "CPA":
                metric = float(campaigns.get('data', {}).get('purchase', 0) or 0)
            # elif kpi == "CPF":
            #     metric = float(campaigns.get('data', {}).get('followers', 0) or 0)
            # elif kpi == "CPS":
            #     metric = float(campaigns.get('data', {}).get('streams', 0) or 0)
            else:
                metric = 0

            # Calculate KPI values (avoid division by zero)
            spent_budget_margin = spent_budget / (campaigns['nomarginbudget']/campaigns['total_budget'])

            kpi_with_margin = round(spent_budget_margin / metric, 3) if metric > 0 else 0
            kpi_no_margin = round((spent_budget) / metric, 3) if metric > 0 else 0  # Assuming 20% margin

            # Calculate objective KPI with total budget
            if metric > 0:
                objective_target= int(round(float(campaigns['nomarginbudget']) / kpi_no_margin, 0))
            else:
                objective_target = 0

            # Repetition : reach / impression
            impression = float(campaigns.get('data', {}).get('impression', 0) or 0)
            reach = float(campaigns.get('data', {}).get('reach', 0) or 0)
            repetition = round(impression / reach, 3) if reach > 0 else 0

            # CTR : clic / impression
            clic = float(campaigns.get('data', {}).get('clic', 0) or 0)
            ctr = round((clic / impression) * 100, 3) if impression > 0 else 0

            # VTR : view / impression
            view = float(campaigns.get('data', {}).get('view', 0) or 0)
            vtr = round((view / impression) * 100, 3) if impression > 0 else 0

            # ROAS : purchase_value / spent_budget
            purchase_value = float(campaigns.get('data', {}).get('purchase_value', 0) or 0)
            roas_margin = round(purchase_value / spent_budget_margin, 3) if spent_budget_margin > 0 else 0
            roas_no_margin = round(purchase_value / spent_budget, 3) if spent_budget > 0 else 0

            # CPC / CPM / CPV / CPA margé
            cpc_margin = round(spent_budget_margin / clic, 3) if clic > 0 else 0
            cpm_margin = round(spent_budget_margin / (impression / 1000), 3) if impression > 0 else 0
            cpv_margin = round(spent_budget_margin / view, 3) if view > 0 else 0
            cpa_margin = round(spent_budget_margin / float(campaigns.get('data', {}).get('purchase', 0) or 0), 3) if float(campaigns.get('data', {}).get('purchase', 0) or 0) > 0 else 0

            # CPC / CPM / CPV / CPA non margé
            cpc_no_margin = round(spent_budget / clic, 3) if clic > 0 else 0
            cpm_no_margin = round(spent_budget / (impression / 1000), 3) if impression > 0 else 0
            cpv_no_margin = round(spent_budget / view, 3) if view > 0 else 0
            cpa_no_margin = round(spent_budget / float(campaigns.get('data', {}).get('purchase', 0) or 0), 3) if float(campaigns.get('data', {}).get('purchase', 0) or 0) > 0 else 0
            marge = round(1 - (campaigns['nomarginbudget']/campaigns['total_budget']), 3) if campaigns['total_budget'] > 0 else 0
            benefices_total = campaigns['total_budget']-campaigns['nomarginbudget']



            
            @staticmethod
            def format_number_french_style(value, suffix="", decimals=2):
                """
                Formate un nombre avec des espaces tous les 3 chiffres avant la virgule, 
                avec option de suffixe comme '€' et nombre de décimales personnalisable.
                
                Exemples :
                    4500 → "4 500,00"
                    1234.55 → "1 234,55"
                    8900 → "8 900,00 €"
                """
                try:
                    number = float(value)
                except (ValueError, TypeError):
                    return str(value)

                # Arrondi personnalisé
                parts = f"{number:.{decimals}f}".split(".")
                integer_part = parts[0]
                decimal_part = parts[1] if decimals > 0 else ""

                # Groupement des milliers
                reversed_digits = integer_part[::-1]
                grouped = " ".join(reversed_digits[i:i+3] for i in range(0, len(reversed_digits), 3))
                formatted_integer = grouped[::-1]

                if decimals > 0:
                    return f"{formatted_integer},{decimal_part} {suffix}".strip()
                else:
                    return f"{formatted_integer} {suffix}".strip()


            campaigns_data.append([
                campaigns['id'],  # ID
                # datetime.fromtimestamp(campaigns['created_at'] / 1000, tz=timezone.utc).strftime('%Y-%m-%d'),  # created_at
                # campaigns['campaigngroup_id'],  # campaigngroup_id
                campaigns['campaign_name'],  # campaign_name
                campaigns['leveler'],  # leveler
                campaigns['objective'],  # objective
                kpi,
                str(campaigns['total_budget']) + " €",  # total_budget
                str(campaigns['nomarginbudget']) + " €",  # nomarginbudget
                campaigns['state'],  # state
                campaigns['start_date'],  # start_date (format: 'YYYY-MM-DD' attendu)
                campaigns['end_date'],  # end_date (format: 'YYYY-MM-DD' attendu)
                campaigns['public_id'],  # ID_Campaign
                campaigns['campaign_public_name'],
                campaigns['ID_adminadaccount'],  # ID_adminadaccount
                campaigns['ID_AdAccount'],  # ID_AdAccount
                campaigns['ID_Campaign'],  # ID_Campaign
                str(round(campaigns.get('data', {}).get('spent_budget', "NA"),2)) + " €",  # Budget dépensé
                format_number_french_style(campaigns.get('data', {}).get('impression', "NA"),decimals=0),  # Impressions
                format_number_french_style(campaigns.get('data', {}).get('view', "NA"),decimals=0),  # Vues
                format_number_french_style(campaigns.get('data', {}).get('clic', "NA"),decimals=0),  # Clics
                format_number_french_style(campaigns.get('data', {}).get('reach', "NA"),decimals=0),  # Portée
                format_number_french_style(campaigns.get('data', {}).get('purchase', "NA"),decimals=0),  # Achats (nombre)
                format_number_french_style(campaigns.get('data', {}).get('purchase_value', "NA"),"€",decimals=2),  # Valeur des achats
                format_number_french_style(campaigns.get('data', {}).get('add_to_cart', "NA"),decimals=0),  # Ajouts au panier
                format_number_french_style(campaigns.get('data', {}).get('add_to_cart_value', "NA"),"€",decimals=2), # Valeur des ajouts au panier
                kpi_with_margin,
                kpi_no_margin,
                round(spent_budget_margin,2),
                objective_target,
                # ➕ KPIs margés
                cpc_margin,
                cpm_margin,
                cpv_margin,
                cpa_margin,

                # ➖ KPIs non margés
                cpc_no_margin,
                cpm_no_margin,
                cpv_no_margin,
                cpa_no_margin,

                # ➕ Autres KPIs
                repetition,
                ctr,
                vtr,

                # ➕ ROAS
                roas_margin,
                roas_no_margin,
                # ➕ Finaciaire
                marge,
                benefices_total
            ])



        return campaigns_data, campaign_columns_index

        


    def fetch_invoice(self,project_id):


        

        data = {
            'token': TokenManager.get_token(),
            'project_id': project_id
        }
        # print("Debut du fetch des campaign")
        # print("data envoyé a xano: ", data)

        response = requests.get(self.route_getinvoice, params=data)
        # print("reponse statut : ", response)
        # print("reponse : ", response.json())
        invoice_data = []

        for invoice in response.json():
            invoice_data.append([
                invoice['id'],  # ID
                # datetime.fromtimestamp(campaigns['created_at'] / 1000, tz=timezone.utc).strftime('%Y-%m-%d'),  # created_at
                # campaigns['campaigngroup_id'],  # campaigngroup_id
                invoice['nom'],  # campaign_name
                invoice['invoice_num'],  # leveler
                invoice['date_sent'],  # objective
                invoice['price_ttc'],  # total_budget
                invoice['state'],  # nomarginbudget
                invoice['invoice_file'],  # state
                invoice['public_id'],  # start_date (format: 'YYYY-MM-DD' attendu)


            ])

        return invoice_data

    def post_account(self, account_name, logo_path):

        
        # Préparer les données du formulaire
        data = {
            "account_name": account_name,
            'logo': None,
            'token': TokenManager.get_token()
        }
        
        # Vérifier si un logo est fourni
        files = {"image": open(logo_path, "rb")} if logo_path else None
        
        
        try:
            response = requests.post(self.route_postaccount, data=data, files=files)
            response_data = response.json()  # Convertir la réponse en JSON
            # print(response_data)

            if response.status_code == 200:
                if response_data == "Account name already exist":
                    return False, "Le compte existe déjà."
                print("Compte ajouté avec succès :", response_data)
                return True, response_data
            else:
                print("Erreur :", response_data)
                return False, response_data
            
            

        except Exception as e:
            print("Erreur de connexion :", e)
            return None
        



    def post_project(self, account_id, project_name, logo_path):

        
        # Préparer les données du formulaire
        data = {
            "account_id": account_id,
            "Project_Name": project_name,
            'logo': None,
            'token': TokenManager.get_token()
        }
        
        # Vérifier si un logo est fourni
        files = {"image": open(logo_path, "rb")} if logo_path else None
        
        
        try:
            response = requests.post(self.route_postproject, data=data, files=files)
            response_data = response.json()  # Convertir la réponse en JSON
            # print(response_data)

            if response.status_code == 200:
                if response_data == "Project name already exist":
                    return False, "Le projet existe déjà."
                print("Projet ajouté avec succès :", response_data)
                return True, response_data
            else:
                print("Erreur :", response_data)
                return False, response_data
            
            

        except Exception as e:
            print("Erreur de connexion :", e)
            return None
        

    def post_groupcampaign(self, project_id, groupcampaign_name):


        # Préparer les données du formulaire
        data = {
            "project_id": project_id,
            "groupname": groupcampaign_name,
            'token': TokenManager.get_token()
        }
        

        try:
            response = requests.post(self.route_postgroupcampaign, data=data)
            response_data = response.json()  # Convertir la réponse en JSON
            # print(response_data)

            if response.status_code == 200:
                if response_data == "Group name already exist":
                    return False, "Le Groupe de campagne existe déjà."
                print("Group Campagne ajouté avec succès :", response_data)
                return True, response_data
            else:
                print("Erreur :", response_data)
                return False, response_data
            
            

        except Exception as e:
            print("Erreur de connexion :", e)
            return None
        

    def post_campaign(self, data):

        data = data
        data['token'] = TokenManager.get_token()

        try:
            response = requests.post(self.route_postcampaign, data=data)
            response_data = response.json()  # Convertir la réponse en JSON
            # print(response_data)

            if response.status_code == 200:
                # if response_data == "":
                #     return False, "Le Groupe de campagne existe déjà."
                # print("Campagne ajouté avec succès :", response_data)
                return True, response_data
            
            else:
                print("Erreur :", response_data)
                return False, response_data
            
            

        except Exception as e:
            print("Erreur de connexion :", e)
            return None

    def modify_campaign(self, data):

        data = data
        data['token'] = TokenManager.get_token()

        try:
            response = requests.put(self.route_editcampaign, data=data)
            response_data = response.json()  # Convertir la réponse en JSON
            # print(response_data)

            if response.status_code == 200:
                # if response_data == "":
                #     return False, "Le Groupe de campagne existe déjà."
                # print("Campagne modifiée avec succès :", response_data)
                return True, response_data
            
            else:
                print("Erreur :", response_data)
                return False, response_data
            
            

        except Exception as e:
            print("Erreur de connexion :", e)
            return None



    def modify_groupcampaign(self, project_id, groupcampaign_name,groupcampaign_id):


        # Préparer les données du formulaire
        data = {
            "project_id": project_id,
            "groupname": groupcampaign_name,
            "campaigngroup_id":groupcampaign_id,
            'token': TokenManager.get_token()
        }
        

        try:
            response = requests.put(self.route_editgroupcampaign, data=data)
            response_data = response.json()  # Convertir la réponse en JSON
            # print(response_data)

            if response.status_code == 200:
                if response_data == "Not right project number":
                    return False, "Pas le bon id de projet. Contactez le dévellopeur."
                print("Group Campagne modifié avec succès :", response_data)
                return True, response_data
            else:
                print("Erreur :", response_data)
                return False, response_data
            
            

        except Exception as e:
            print("Erreur de connexion :", e)
            return None
        

    

    def delete_groupcampaign(self, project_id, groupcampaign_name):

        # Préparer les données du formulaire
        data = {
            "project_id": project_id,
            "campaigngroup_id": groupcampaign_name,
            'token': TokenManager.get_token()
        }
        print("data",data)
        

        try:
            response = requests.delete(self.route_delgroupcampaign, params=data)
            response_data = response.json()  # Convertir la réponse en JSON
            # print(response_data)

            if response.status_code == 200:
                if response_data == "Campaigns under this group exist":
                    return False, "Campaigns under this group exist, please first delete associated campaigns before deleting group"
                if response_data == "Not right project number":
                    return False, "Not right project number"
                
                print("Groupe Campagne supprimé", response_data)
                return True, response_data
            else:
                print("Erreur :", response_data)
                return False, response_data
            
            

        except Exception as e:
            print("Erreur de connexion :", e)
            return None

    def upload_invoice_to_s3(self, file_path: str, filename: str) -> str:
        """
        Upload un fichier PDF directement vers S3 (sans dossier, sans presigned URL).
        Le fichier est placé à la racine du bucket.
        :param file_path: chemin local du fichier à envoyer
        :param filename: nom du fichier final (ex: SAcorp_invoice_123.pdf)
        :return: clé S3 de l’objet
        """
        try:
            session = boto3.session.Session(
                aws_access_key_id=self.aws_access_key,
                aws_secret_access_key=self.aws_secret_key,
                region_name=self.aws_region
            )

            s3 = session.client("s3")

            with open(file_path, 'rb') as f:
                s3.upload_fileobj(
                    Fileobj=f,
                    Bucket=self.aws_invoice_bucket_name,
                    Key=filename,
                    ExtraArgs={
                        "ContentType": "application/pdf"
                    }
                )

            return filename  # ou juste signaler "ok"
        except NoCredentialsError:
            raise RuntimeError("Credentials AWS non valides")
        except Exception as e:
            raise RuntimeError(f"Erreur upload S3 : {str(e)}")


    def post_invoice(self,project_id,name, number, price, state,date, file_name ):
        """
        Envoie une nouvelle facture à Xano via l'endpoint POST.
        :param project_id: ID du projet lié
        :param name: nom de la facture (champ "nom" dans la DB)
        :param number: numéro de facture
        :param price: montant TTC (entier)
        :param state: état de la facture (enum : ex. "Waiting", "Paid", etc.)
        :param date_sent: date d'envoi (format YYYY-MM-DD)
        :param file_name: nom du fichier PDF dans S3
        """

        data = {
            "project_id": project_id,
            "nom": name,
            "invoice_num": number,
            "price_ttc": price,
            "state": state,
            "date_sent": date,
            "invoice_file": file_name,  # ex: SAcorp_XXX.pdf
            "token": TokenManager.get_token()
        }

        try:
            response = requests.post(self.route_postinvoice, data=data)
            response_data = response.json()
            print(response_data)

            if response.status_code == 200:
                print("Facture ajoutée avec succès :", response_data)
                return True, response_data
            else:
                print("Erreur API :", response_data)
                return False, response_data

        except Exception as e:
            print("Erreur de connexion à l'API :", str(e))
            return None

    def modify_invoice(self, data):
        '''Modifier une facture sur xano'''

        data['token'] = TokenManager.get_token()

        try:
            response = requests.put(self.route_editinvoice, data=data)
            response_data = response.json()
            print(response_data)

            if response.status_code == 200:
                print("Facture modifiée avec succès :", response_data)
                return True, response_data
            else:
                print("Erreur API :", response_data)
                return False, response_data

        except Exception as e:
            print("Erreur de connexion à l'API :", str(e))
            return None


    def delete_invoice(self, invoice_id):
        '''Suprimer une facture sur xano'''

        data = {
            "invoice_id": invoice_id,
            "token": TokenManager.get_token()
        }
        try:
            response = requests.delete(self.route_deleteinvoice, data=data)
            response_data = response.json()
            print(response_data)

            if response.status_code == 200:
                print("Facture supprimé avec succès :", response_data)
                return True, response_data
            else:
                print("Erreur API :", response_data)
                return False, response_data

        except Exception as e:
            print("Erreur de connexion à l'API :", str(e))
            return None



        return

    def delete_invoice_from_s3(self, filename: str) -> str:
        """
        Upload un fichier PDF directement vers S3 (sans dossier, sans presigned URL).
        Le fichier est placé à la racine du bucket.
        :param file_path: chemin local du fichier à envoyer
        :param filename: nom du fichier final (ex: SAcorp_invoice_123.pdf)
        :return: clé S3 de l’objet
        """
        try:
            session = boto3.session.Session(
                aws_access_key_id=self.aws_access_key,
                aws_secret_access_key=self.aws_secret_key,
                region_name=self.aws_region
            )

            s3 = session.client("s3")


            s3.delete_object(
                Bucket=self.aws_invoice_bucket_name,
                Key=filename
            )   

            return filename  # ou juste signaler "ok"
        except NoCredentialsError:
            raise RuntimeError("Credentials AWS non valides")
        except Exception as e:
            raise RuntimeError(f"Erreur suppression S3 : {str(e)}")







class TokenManager:
    @staticmethod
    def get_token():
        # Première partie (avant le -)
        parts1 = [
            'p2U', 'fvK', 'irf', '166M',
            'Q9T', 'yWg', 'RS1', 'ujJ4B',
            'NQR2J', 'lADT', 'gNpb', 'Uhb',
            'MjWD'
        ]
        
        # Deuxième partie (après le -)
        parts2 = [
            '8S', '_wwg', 'W8j', 'e2L',
            'qt2a', '49ts', 'TYbb', 'zS07C',
            'McEM', 'ErEl', '9aVq', 'Iof',
            'TGxi', 'JnoP', '1WKj', 'V2Kp',
            'RIXv', 'Z6yG', 'H55r', 'BlY',
            '-m12', 'WNhI', 'tker', 'UmRb',
            'tGke', 'iXlQ', 'NeOt', '2HTN',
            'w3LL', 'xO_L', 'Ll1p', 'I9kA'
        ]
        
        # Assemble les parties sans inversion
        token1 = ''.join(parts1)
        token2 = ''.join(parts2)
        
        return f"{token1}-{token2}"
    

class AwsCredentialManager:
    @staticmethod
    def get_access_key():
        parts = [
            'AKIA', 'WQUO', 'ZQYK', '2SMA', 'BY5F'  # découpe sécurisée
        ]
        return ''.join(parts)

    @staticmethod
    def get_secret_key():
        parts = [
            '2ZOb', 'EgZ+', '1B1m', 'EdS4', 'rCw4',
            'BAF2', 'YZ46', 'T6BE', 'ma1l', 'jp2w'
        ]
        return ''.join(parts)
