
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                            QTableView, QMessageBox, QHeaderView, QDialog, QLabel,
                            QLineEdit, QFormLayout, QDateEdit, QComboBox,QFileDialog,QProgressDialog)
from PyQt6.QtCore import Qt, QDate
from PyQt6.QtGui import QStandardItemModel, QStandardItem, QPixmap, QDoubleValidator
import os
import sys
import re
import boto3



class InvoiceTableWidget(QWidget):
    def __init__(self,font_manager,controller):
        super().__init__()

        self.font_manager = font_manager
        self.controller = controller
        self.selected_pdf_path = None
        # Création du modèle avec colonnes
        self.model = QStandardItemModel(0, 7)  # 0 lignes, 6 colonnes
        self.model.setHorizontalHeaderLabels(["ID","Nom",
                                              "num. facture", "Date","Prix TTC", "Etat","invoice_file","public_id"])




        self.locked_columns = [0,1,2,3,4,5,6]  #  Colonnes verrouillées (ID, Date de création, Nombre de campagnes)
        self.modified_rows = set()  #  Stocke les lignes modifiées



        # Création du tableau
        self.table = QTableView()
        self.table.setSortingEnabled(True)  #  Active le tri au clic
        self.table.setModel(self.model)
        self.table.setFixedSize(800, 250)


        # Activer la sélection de toute la ligne
        self.table.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.table.setSelectionMode(QTableView.SelectionMode.SingleSelection)

        # Définir le mode de redimensionnement par défaut pour toutes les colonnes
        self.table.horizontalHeader().setDefaultSectionSize(100)
        self.table.horizontalHeader().setStretchLastSection(True)  # Étire la dernière colonne

        # Définir des largeurs spécifiques pour chaque colonne
        column_widths = {
            0: 35,    # ID (très petit)
            1: 200,   # Nom facture (encore plus grand)
            2: 100,    # numéro
            3: 100,    # date
            4: 90,    # prix ttc
            5: 70,    # Etat
            6: 100,    # Invoice file
            7: 70,    # public_id
        }

        # Appliquer les largeurs de colonnes
        for col_index, width in column_widths.items():
            self.table.setColumnWidth(col_index, width)
            # Définir le mode de redimensionnement pour cette colonne
            self.table.horizontalHeader().setSectionResizeMode(col_index, QHeaderView.ResizeMode.Interactive)

        self.table.setStyleSheet("""
            QTableView::item:selected {
                background-color: #476AFF;
                color: white;
            }
        """)




        # Layout principal
        mainlayout = QVBoxLayout()
        # Titre en gras
        title_label = QLabel("Factures")
        title_label.setFont(self.font_manager.get_font("bold", 16))
        title_label.setAlignment(Qt.AlignmentFlag.AlignLeft)
        mainlayout.addWidget(title_label)
        
        # Limiter la hauteur du layout principal
        
        
        self.setFixedHeight(300)


        # Add buttons for "Creer", "Modifier", "Supprimer"
        button_layout = QHBoxLayout()
        button_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.create_button = QPushButton("Creer")
        self.create_button.setMaximumWidth(100)
        self.create_button.setEnabled(False)

        self.modify_button = QPushButton("Modifier")
        self.modify_button.setMaximumWidth(100)
        self.modify_button.setEnabled(False)

        self.view_button = QPushButton("View")
        self.view_button.setMaximumWidth(100)
        self.view_button.setEnabled(False)

        self.delete_button = QPushButton("Supprimer")
        self.delete_button.setMaximumWidth(100)
        self.delete_button.setEnabled(False)

        button_layout.addWidget(self.create_button)
        button_layout.addWidget(self.modify_button)
        button_layout.addWidget(self.view_button)
        button_layout.addWidget(self.delete_button)

        mainlayout.addLayout(button_layout)
        mainlayout.addWidget(self.table)




        self.setLayout(mainlayout)




    def update_data(self, data):
        """ Met à jour les données du tableau en gardant les colonnes verrouillées"""
        self.model.removeRows(0, self.model.rowCount())  #  Supprime les anciennes lignes

        for row, values in enumerate(data):
            for col, value in enumerate(values):
                item = QStandardItem(str(value))
                if isinstance(value, int):  #  Permet un tri correct des nombres
                    item.setData(value, Qt.ItemDataRole.EditRole)

                #  Désactiver l'édition pour les colonnes verrouillées
                if col in self.locked_columns:
                    item.setFlags(item.flags() & ~Qt.ItemFlag.ItemIsEditable)

                self.model.setItem(row, col, item)


                # Colorer la cellule "state" en fonction de sa valeur
                if col == 5:  # Colonne "state"
                    if value == "Paid":
                        item.setBackground(Qt.GlobalColor.green)
                    elif value == "Waiting":
                        item.setBackground(Qt.GlobalColor.yellow)



    def get_table(self):
        """Retourne le QTableView pour être ajouté ailleurs"""
        return self.table

    def open_add_invoice_window(self,account_id_selected,account_name_selected,project_id_selected,project_name_selected,controller):
        """Ouvre une nouvelle fenêtre pour ajouter un projet"""
        dialog = AddinvoiceDialog(account_id_selected,account_name_selected,project_id_selected,project_name_selected,controller,self.font_manager)
        dialog.exec()  #  Affiche la boîte de dialogue en mode bloquant

    def open_modify_invoice_window(self,account_id_selected,account_name_selected,project_id_selected,project_name_selected,controller):
        """Ouvre une nouvelle fenêtre pour ajouter un projet"""
        selection_model = self.table.selectionModel()
        selected_rows = selection_model.selectedRows()
        if not selected_rows:
            QMessageBox.warning(self, "Erreur", "Veuillez sélectionner une facture.")
            return

        selected_index = selected_rows[0]
        invoice_data ={
            "id": int(self.model.item(selected_index.row(), 0).text()),
            "nom": self.model.item(selected_index.row(), 1).text(),
            "invoice_num": self.model.item(selected_index.row(), 2).text(),
            "date_sent": self.model.item(selected_index.row(), 3).text(),
            "price_ttc": self.model.item(selected_index.row(), 4).text(),
            "state": self.model.item(selected_index.row(), 5).text(),
            "invoice_file": self.model.item(selected_index.row(), 6).text(),
            "public_id": self.model.item(selected_index.row(), 7).text(),
        }

        print("selected invoice : ", invoice_data)

        dialog = AddinvoiceDialog(account_id_selected,account_name_selected,project_id_selected,project_name_selected,controller,self.font_manager,invoice_data)
        dialog.exec()  #  Affiche la boîte de dialogue en mode bloquant

    def open_view_invoice_window(self,account_id_selected,account_name_selected,project_id_selected,project_name_selected,group_id_selected,group_name_selected,controller):
        """Ouvre une nouvelle fenêtre pour ajouter un projet"""
        selection_model = self.table.selectionModel()
        selected_rows = selection_model.selectedRows()
        if not selected_rows:
            QMessageBox.warning(self, "Erreur", "Veuillez sélectionner une facture.")
            return

        selected_index = selected_rows[0]
        campaign_id = int(self.model.item(selected_index.row(), 0).text())
        print("selected campaign id : ", campaign_id)

        dialog = AddinvoiceDialog(account_id_selected,account_name_selected,project_id_selected,project_name_selected,group_id_selected,group_name_selected,controller,self.font_manager,campaign_id=campaign_id,read_only=True)
        dialog.exec()  #  Affiche la boîte de dialogue en mode bloquant

    def del_invoice(self, project_id_selected):
        
        """Supprime un groupe de campagne"""
        selection_model = self.table.selectionModel()
        selected_rows = selection_model.selectedRows()
        if not selected_rows:
            msg_box = QMessageBox()
            msg_box.setWindowTitle("Erreur")
            msg_box.setText("Veuillez sélectionner une facture.")

            # Définir une icône personnalisée
            if getattr(sys, 'frozen', False):
                base_path = sys._MEIPASS
                assets_path = os.path.join(base_path, "assets", "icons")
            else:
                assets_path = os.path.abspath("assets/icons/")

            icon_path = os.path.join(assets_path, "custom_error.png")
            if os.path.exists(icon_path):
                msg_box.setIconPixmap(QPixmap(icon_path).scaled(48, 48, Qt.AspectRatioMode.KeepAspectRatio))
            msg_box.exec()
            return

        selected_index = selected_rows[0]
        invoice_id = int(self.model.item(selected_index.row(), 0).text())
        invoice_file = self.model.item(selected_index.row(), 6).text()
        print("invoice file : ", invoice_file)
        print("invoice id : ", invoice_id)

        # Confirmation de suppression
        confirmation = QMessageBox.question(
            self,
            "Confirmation de suppression",
            "Êtes-vous sûr de vouloir supprimer cette facture ?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if confirmation != QMessageBox.StandardButton.Yes:
            return


        # Suppression du fichier S3
        print("----- supression S3")
        success_s3 = self.delete_invoice_to_s3(invoice_file)
        if not success_s3:
            msg_box = QMessageBox()
            msg_box.setWindowTitle("Erreur")
            msg_box.setText("Échec de la suppression du fichier sur S3.")
            if getattr(sys, 'frozen', False):
                base_path = sys._MEIPASS
                assets_path = os.path.join(base_path, "assets", "icons")
            else:
                assets_path = os.path.abspath("assets/icons/")
            icon_path = os.path.join(assets_path, "custom_error.png")
            if os.path.exists(icon_path):
                msg_box.setIconPixmap(QPixmap(icon_path).scaled(48, 48, Qt.AspectRatioMode.KeepAspectRatio))
            msg_box.exec()
            return

        # Suppression dans Xano
        print("----- supression Xano")
        success_xano = self.delete_invoice_from_xano(invoice_id)
        if not success_xano:
            msg_box = QMessageBox()
            msg_box.setWindowTitle("Erreur")
            msg_box.setText("Échec de la suppression de la facture dans Xano.")
            if getattr(sys, 'frozen', False):
                base_path = sys._MEIPASS
                assets_path = os.path.join(base_path, "assets", "icons")
            else:
                assets_path = os.path.abspath("assets/icons/")
            icon_path = os.path.join(assets_path, "custom_error.png")
            if os.path.exists(icon_path):
                msg_box.setIconPixmap(QPixmap(icon_path).scaled(48, 48, Qt.AspectRatioMode.KeepAspectRatio))
            msg_box.exec()
            return

        # Message de succès si tout s'est bien passé
        msg_box = QMessageBox()
        msg_box.setWindowTitle("Succès")
        msg_box.setText("Facture supprimée avec succès.")
        if getattr(sys, 'frozen', False):
            base_path = sys._MEIPASS
            assets_path = os.path.join(base_path, "assets", "icons")
        else:
            assets_path = os.path.abspath("assets/icons/")
        icon_path = os.path.join(assets_path, "custom_success.png")
        if os.path.exists(icon_path):
            msg_box.setIconPixmap(QPixmap(icon_path).scaled(48, 48, Qt.AspectRatioMode.KeepAspectRatio))
        msg_box.exec()
        self.controller.update_invoices()




        return

    def delete_invoice_to_s3(self, file_name):

        return self.controller.del_invoice_file_from_S3(file_name)

        

    def delete_invoice_from_xano(self, invoice_id) :

        return self.controller.del_invoice_from_xano(invoice_id)



class AddinvoiceDialog(QDialog):
    def __init__(self,account_id,account_name,project_id,project_name,controller,font_manager,invoice_data=None):
        super().__init__()

        self._init_metadata(
            account_id, account_name,
            project_id, project_name,
            controller
        )
        if invoice_data is not None:
            self.invoice_data = invoice_data
        else:
            self.invoice_data = None



        self.setWindowTitle("Modifier une facture" if invoice_data else "Créer une nouvelle facture")
        self.setFixedSize(900, 900)

        layout = QVBoxLayout()
        layout.setAlignment(Qt.AlignmentFlag.AlignTop)


        layout.addLayout(self._build_header_info(font_manager))
        layout.addSpacing(40)
        layout.addWidget(self._build_title_section(font_manager))

        layout.addSpacing(40)
        layout.addLayout(self._build_form_layout(font_manager))

        if self.invoice_data:
            self.populate_fields_from_invoice_data(self.invoice_data)

        self.setLayout(layout)

    def _init_metadata(self, account_id, account_name, project_id, project_name, controller):
        self.controller = controller
        self.account_id = account_id
        self.account_name = account_name
        self.project_id = project_id
        self.project_name = project_name

    def populate_fields_from_invoice_data(self, invoice_data):
        self.invoice_id_xano = invoice_data.get("id")
        self.invoice_name_input.setText(invoice_data.get("nom", ""))
        self.invoice_num_input.setText(invoice_data.get("invoice_num", ""))
        self.invoice_price_input.setText(str(invoice_data.get("price_ttc", "")))
        self.state_dropdown.setCurrentText(invoice_data.get("state", "Waiting"))
        try:
            self.date_input.setDate(QDate.fromString(invoice_data.get("date_sent", ""), "yyyy-MM-dd"))
        except:
            pass
        self.pdf_file_label.setText(invoice_data.get("invoice_file", ""))
        self.upload_pdf_button.setVisible(False)
        self.pdf_info_label.setVisible(False)


        

        # self.campaign_name_input.setText(campaign_data.get("campaign_name", ""))
        # levier = campaign_data.get("leveler", "")
        # if levier in LEVIER_OBJECTIFS_MAP:
        #     self.levier_dropdown.setCurrentText(levier)
        #     self.update_objectif_options()
        #     objectif = campaign_data.get("objective", "")
        #     self.objectif_dropdown.setCurrentText(objectif)
        # self.budget_marge_input.setText(str(campaign_data.get("total_budget", "")))
        # self.budget_non_marge_input.setText(str(campaign_data.get("nomarginbudget", "")))
        # self.state_dropdown.setCurrentText(campaign_data.get("state", "Active"))
        # try:
        #     self.start_date_input.setDate(QDate.fromString(campaign_data.get("start_date", ""), "yyyy-MM-dd"))
        #     self.end_date_input.setDate(QDate.fromString(campaign_data.get("end_date", ""), "yyyy-MM-dd"))
        # except:
        #     pass
        # self.campaign_id_input.setText(campaign_data.get("ID_Campaign", ""))

        # # Préselectionner Admin et Ad Account si présents dans les données
        # for i in range(self.admin_account_dropdown.count()):
        #     item = self.admin_account_dropdown.itemData(i)
        #     if item and item.get("id_account") == campaign_data.get("ID_adminadaccount"):
        #         self.admin_account_dropdown.setCurrentIndex(i)
        #         break
        # self.update_ad_account_options()
        # for i in range(self.ad_account_dropdown.count()):
        #     item = self.ad_account_dropdown.itemData(i)
        #     if item and item.get("id_account") == campaign_data.get("ID_AdAccount"):
        #         self.ad_account_dropdown.setCurrentIndex(i)
        #         break
        return


    def _build_header_info(self, font_manager):
        layout = QVBoxLayout()

        # Labels pour les informations du compte et du projet
        self.label2 = QLabel("Account :")
        self.label22 = QLabel(str(self.account_name))
        self.label3 = QLabel("- ID Xano:")
        self.label32 = QLabel(str(self.account_id))
        self.label4 = QLabel("Projet :")
        self.label42 = QLabel(str(self.project_name))
        self.label5 = QLabel("- Project ID Xano:")
        self.label52 = QLabel(str(self.project_id))

        self.label2.setFont(font_manager.get_font("bold", 14))
        self.label22.setFont(font_manager.get_font("regular", 14))
        self.label3.setFont(font_manager.get_font("bold", 14))
        self.label32.setFont(font_manager.get_font("regular", 14))
        self.label4.setFont(font_manager.get_font("bold", 14))
        self.label42.setFont(font_manager.get_font("regular", 14))
        self.label5.setFont(font_manager.get_font("bold", 14))
        self.label52.setFont(font_manager.get_font("regular", 14))

        # Layout pour les informations du compte
        row1_layout = QHBoxLayout()
        row1_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)
        row1_layout.addWidget(self.label2)
        row1_layout.addWidget(self.label22)
        row1_layout.addWidget(self.label3)
        row1_layout.addWidget(self.label32)

        # Layout pour les informations du projet
        row2_layout = QHBoxLayout()
        row2_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)
        row2_layout.addWidget(self.label4)
        row2_layout.addWidget(self.label42)
        row2_layout.addWidget(self.label5)
        row2_layout.addWidget(self.label52)

        layout.addLayout(row1_layout)
        layout.addLayout(row2_layout)

        return layout


    def _build_title_section(self, font_manager):
        title_layout = QVBoxLayout()

        title_label = QLabel("Modifier une facture" if self.invoice_data else "Créer une nouvelle facture")
        title_label.setFont(font_manager.get_font("bold", 20))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_layout.addWidget(title_label)

        # Ajouter un bouton de visualisation de la facture
        # view_invoice_layout = QHBoxLayout()
        # view_invoice_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # self.view_invoice_button = QPushButton("Voir la facture")
        # self.view_invoice_button.setFixedWidth(200)
        # # self.view_invoice_button.clicked.connect(self.import_campaign_params) AREMETRE
        # view_invoice_layout.addWidget(self.view_invoice_button)

        # title_layout.addLayout(view_invoice_layout)

        # Ajouter un label pour les messages d'erreur d'importation
        self.view_invoice_button = QLabel("")
        self.view_invoice_button.setStyleSheet("color: red;")
        self.view_invoice_button.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_layout.addWidget(self.view_invoice_button)

        container = QWidget()
        container.setLayout(title_layout)
        return container


    def _build_form_layout(self, font_manager):
        form_layout = QFormLayout()
        # Formulaire pour les champs de la campagne
        form_layout = QFormLayout()
        form_layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.ExpandingFieldsGrow)  # Permet aux champs de grandir
        form_layout.setRowWrapPolicy(QFormLayout.RowWrapPolicy.DontWrapRows)  # Empêche les lignes de se replier
        form_layout.setVerticalSpacing(20)  # Ajoute un espacement vertical entre les champs

        # Nom de la facture
        self.invoice_name_input = QLineEdit()
        self.invoice_name_input.setFixedWidth(600)  # Augmente la taille de l'input
        form_layout.addRow("Nom de la facture :", self.invoice_name_input)
        

        spacer = QWidget()
        spacer.setFixedHeight(20)
        form_layout.addRow(spacer)

        #######   Attributs   ########

        # Ajouter un titre pour Levier et Objectif
        form_layout.addRow(self._create_section_title("Attributs", font_manager))

        # State (enum), Start date, End date
        self.invoice_num_input = QLineEdit()
        self.invoice_num_input.setFixedWidth(100)  # Augmente la taille de l'input

        # State (enum), Start date, End date
        self.invoice_price_input = QLineEdit()
        self.invoice_price_input.setFixedWidth(100)  # Augmente la taille de l'input


        self.state_dropdown = QComboBox()
        self.state_dropdown.addItems(["Waiting","Paid"])
        self.state_dropdown.setFixedWidth(100)

        self.date_input = QDateEdit()
        self.date_input.setFixedSize(150, 30)  # Augmente la taille de la box
        self.date_input.setStyleSheet(self._get_dateedit_style())
        self.date_input.setCalendarPopup(True)
        self.date_input.setDisplayFormat("yyyy-MM-dd")
        self.date_input.setDate(QDate.currentDate())




        # Numero
        num_layout = QHBoxLayout()
        label_num = QLabel("Numéro de la facture :")
        label_num.setFixedWidth(150)
        num_layout.addWidget(label_num)
        num_layout.addWidget(self.invoice_num_input)

        # Montant
        price_layout = QHBoxLayout()
        label_price = QLabel("Montant de la facture :")
        label_price.setFixedWidth(150)
        price_layout.addWidget(label_price)
        price_layout.addWidget(self.invoice_price_input)

        # State
        state_layout = QHBoxLayout()
        label_state = QLabel("État :")
        label_state.setFixedWidth(50)
        state_layout.addWidget(label_state)
        state_layout.addWidget(self.state_dropdown)

        # Start date
        date_layout = QHBoxLayout()
        label_date = QLabel("Date d'émission :")
        label_date.setFixedWidth(100)
        date_layout.addWidget(label_date)
        date_layout.addWidget(self.date_input)


        form_layout.addRow(num_layout)
        form_layout.addRow(price_layout)
        form_layout.addRow(state_layout)
        form_layout.addRow(date_layout)


        # Bouton pour uploader un fichier PDF
        self.upload_pdf_button = QPushButton("Choisir un fichier PDF")
        self.upload_pdf_button.clicked.connect(self.select_pdf_file)
        self.upload_pdf_button.setFixedWidth(200)

        # Label pour afficher le nom du fichier sélectionné
        self.pdf_file_label = QLabel("Aucun fichier sélectionné")
        self.pdf_file_label.setWordWrap(True)
        self.pdf_file_label.setFixedWidth(400)

        # Label pour afficher la limite de taille
        self.pdf_info_label = QLabel("Limite maximale : 5 Mo")
        self.pdf_info_label.setStyleSheet("color: gray; font-size: 10pt;")
        self.pdf_info_label.setAlignment(Qt.AlignmentFlag.AlignLeft)

        # Disposition horizontale pour le bouton et le nom du fichier
        pdf_layout = QHBoxLayout()
        pdf_layout.addWidget(self.upload_pdf_button)
        pdf_layout.addWidget(self.pdf_file_label)

        # Disposition verticale combinée
        pdf_section_layout = QVBoxLayout()
        pdf_section_layout.addLayout(pdf_layout)
        pdf_section_layout.addWidget(self.pdf_info_label)

        # Ajout au formulaire
        form_layout.addRow("Facture PDF :", pdf_section_layout)

        # Bouton Créer
        self.create_button = QPushButton("Mofidier" if self.invoice_data else "Créer")
        self.create_button.setFixedWidth(150)
        self.create_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                padding: 8px;
                border-radius: 5px;
                font-size: 12pt;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        if not self.invoice_data:
            self.create_button.clicked.connect(self.on_create_clicked)
        else:
            self.create_button.clicked.connect(self.on_modify_clicked)


        # Centrer le bouton dans la ligne du formulaire
        create_button_layout = QHBoxLayout()
        create_button_layout.addStretch()
        create_button_layout.addWidget(self.create_button)
        create_button_layout.addStretch()

        form_layout.addRow("", create_button_layout)




        return form_layout
    
    
    def _create_section_title(self, text, font_manager):
        title = QLabel(text)
        title.setFont(font_manager.get_font("bold", 15))
        title.setStyleSheet("color: #476AFF;")
        return title


    def _get_dateedit_style(self):
        return """
            QDateEdit {
            outline: none;
            border: 1px solid gray;
            border-radius: 5px;
            padding: 2px;
            }
            QDateEdit:focus {
            border: 1px solid #888;
            }

            QCalendarWidget QWidget {
            background-color: white;
            color: black;

            }

            QCalendarWidget QToolButton {
            background-color: none;
            color: black;
            font-weight: bold;
            border: none;
            margin: 5px;
            }

            QCalendarWidget QToolButton:hover {
            background-color: #eeeeee;
            }

            QCalendarWidget QSpinBox {
            width: 70px;
            font-size: 12px;
            color: black;
            background-color: none;
            border: none;
            }

            QCalendarWidget QMenu {
            background-color: white;
            border: 1px solid gray;
            }

            QCalendarWidget QAbstractItemView {
            selection-background-color: #476AFF;
            selection-color: white;
            background-color: #fefefe;
            gridline-color: lightgray;
            }

            QCalendarWidget QHeaderView::section {
            background-color: #476AFF;
            color: white;
            font-weight: bold;
            }

            QCalendarWidget QAbstractItemView::item {
            color: black;
            }

            QCalendarWidget QAbstractItemView::item:enabled:nth-child(7),  /* Saturday */
            QCalendarWidget QAbstractItemView::item:enabled:nth-child(1)   /* Sunday */
            {
            color: #476AFF;
            }
            QCalendarWidget QToolButton#qt_calendar_prevmonth {
            qproperty-icon: url("assets/icons/arrow-circle-left.png");
            qproperty-iconSize: 20px 20px;
            }

            QCalendarWidget QToolButton#qt_calendar_nextmonth {
            qproperty-icon: url("assets/icons/arrow-circle-right.png");
            qproperty-iconSize: 20px 20px;
            }


        """

    def select_pdf_file(self):
        max_size_mb = 5  # Limite de taille en Mo
        file_path, _ = QFileDialog.getOpenFileName(self, "Choisir un fichier PDF", "", "PDF Files (*.pdf)")
        
        if file_path:
            file_size_mb = os.path.getsize(file_path) / (1024 * 1024)  # Convertir en Mo
            
            if file_size_mb > max_size_mb:
                QMessageBox.warning(self, "Fichier trop volumineux", f"Le fichier dépasse la limite de {max_size_mb} Mo.")
                return
            
            self.selected_pdf_path = file_path
            self.pdf_file_label.setText(os.path.basename(file_path))
    

    
    def on_create_clicked(self):
        invoice_name = self.invoice_name_input.text().strip()
        invoice_number = self.invoice_num_input.text().strip()
        invoice_price_text = self.invoice_price_input.text().strip()
        invoice_state = self.state_dropdown.currentText()
        invoice_date = self.date_input.date().toString("yyyy-MM-dd")
        pdf_file = self.selected_pdf_path if hasattr(self, 'selected_pdf_path') else None

        if not invoice_name or not invoice_number or not pdf_file:
            QMessageBox.warning(self, "Champs manquants", "Tous les champs doivent être remplis, y compris un fichier PDF.")
            return
        
        # Validation du prix
        is_valid, invoice_price, error_message = self.validate_price(invoice_price_text)
        if not is_valid:
            QMessageBox.warning(self, "Prix invalide", error_message)
            return
        

        # Progression visuelle
        progress = QProgressDialog("Création de la facture en cours...", "Annuler", 0, 100, self)
        progress.setWindowModality(Qt.WindowModality.WindowModal)
        progress.setValue(0)

        # Étape 1 : upload du fichier PDF
        try:
            progress.setLabelText("Envoi du PDF sur AWS...")
            self.s3_filename = self.upload_invoice_to_s3(pdf_file,invoice_name,invoice_number)
            progress.setValue(50)
        except Exception as e:
            progress.close()
            QMessageBox.critical(self, "Erreur AWS", f"Erreur lors de l'upload S3 : {str(e)}")
            return

        # Étape 2 : envoi des métadonnées à Xano
        try:
            progress.setLabelText("Transmission des données à Xano...")
            self.send_invoice_to_xano(
                project_id= self.project_id,
                name=invoice_name,
                number=invoice_number,
                price = invoice_price,
                state=invoice_state,
                date=invoice_date,
                file_name=self.s3_filename
            )
            progress.setValue(100)
        except Exception as e:
            progress.close()
            QMessageBox.critical(self, "Erreur Xano", f"Erreur lors de l'envoi à Xano : {str(e)}")
            return

        progress.close()
        QMessageBox.information(self, "Succès", "La facture a été créée et transmise avec succès.")
        self.controller.update_invoices()
        self.accept()


    def on_modify_clicked(self):
        invoice_id = self.invoice_id_xano
        invoice_name = self.invoice_name_input.text().strip()
        invoice_number = self.invoice_num_input.text().strip()
        invoice_price_text = self.invoice_price_input.text().strip()
        invoice_state = self.state_dropdown.currentText()
        invoice_date = self.date_input.date().toString("yyyy-MM-dd")
        invoice_data = {
            "invoice_id": invoice_id,
            "nom": invoice_name,
            "invoice_num": invoice_number,
            "price_ttc": invoice_price_text,
            "state": invoice_state,
            "date_sent": invoice_date,
        }



        # Validation du prix
        is_valid, invoice_price, error_message = self.validate_price(invoice_price_text)
        if not is_valid:
            QMessageBox.warning(self, "Prix invalide", error_message)
            return

        # Progression visuelle
        progress = QProgressDialog("Modification de la facture en cours...", "Annuler", 0, 100, self)
        progress.setWindowModality(Qt.WindowModality.WindowModal)
        progress.setValue(0)

        try:
            progress.setLabelText("Mise à jour des données dans Xano...")
            self.controller.modify_invoice_to_xano(
                invoice_data
            )
            progress.setValue(100)
        except Exception as e:
            progress.close()
            QMessageBox.critical(self, "Erreur Xano", f"Erreur lors de la mise à jour dans Xano : {str(e)}")
            return

        progress.close()
        QMessageBox.information(self, "Succès", "La facture a été modifiée avec succès.")
        self.controller.update_invoices()
        self.accept()

        


    

    def upload_invoice_to_s3(self, file_path,invoice_name,invoice_num):

        return self.controller.send_invoice_file_to_S3(file_path,invoice_name,invoice_num)

    def send_invoice_to_xano(self, project_id, name, number, price, state,date, file_name) :

        return self.controller.send_invoice_to_xano(project_id, name, number, price, state,date, file_name)



        # s3 = boto3.client(
        #     's3',
        #     aws_access_key_id='TON_ACCESS_KEY',
        #     aws_secret_access_key='TON_SECRET_KEY',
        #     region_name='eu-west-1'  # adapte à ta région
        # )

        # bucket_name = 'ton-nom-de-bucket'
        # key = f"factures/{os.path.basename(file_path)}"

        # with open(file_path, 'rb') as f:
        #     s3.upload_fileobj(f, bucket_name, key)

        # # Retourne l'URL publique ou présignée (à adapter selon ta config S3)
        # return f"https://{bucket_name}.s3.eu-west-1.amazonaws.com/{key}"``
        # Validation du prix
    def validate_price(self, price_text):
        """
        Valide que le prix est un entier valide.
        
        Args:
            price_text (str): Le texte du prix à valider
            
        Returns:
            tuple: (bool, int|None, str|None) - (succès, valeur convertie, message d'erreur)
        """
        if not price_text.strip():
            return False, None, "Le prix ne peut pas être vide."
        
        # Nettoyer le texte (remplacer virgules par points, supprimer espaces et symboles €)
        cleaned_price = price_text.replace(',', '.').replace(' ', '').replace('€', '')
        
        try:
            # Convertir en float d'abord pour gérer les décimales
            price_float = float(cleaned_price)
            
            # Vérifier si le nombre est positif
            if price_float <= 0:
                return False, None, "Le prix doit être supérieur à zéro."
            
            # Convertir en entier
            price_int = int(price_float)
            
            # Vérifier si la conversion a perdu des décimales significatives
            if abs(price_float - price_int) > 0.01:
                return False, None, "Le prix doit être un nombre entier (sans centimes)."
                
            return True, price_int, None
            
        except ValueError:
            return False, None, "Format de prix invalide. Veuillez entrer un nombre."

