"""
Widget pour la gestion des campagnes dans la vue de toutes les campagnes.
"""
from PyQt6.QtGui import QKeySequence, QShortcut,QIcon
from PyQt6.QtWidgets import QApplication

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                            QTableView, QMessageBox, QHeaderView,QComboBox,QLineEdit,QDateEdit,QCheckBox,QMenu,QLabel,QWidgetAction,QGridLayout,QInputDialog,QFrame)
from PyQt6.QtCore import Qt, QDate
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QStandardItemModel, QStandardItem, QColor,QAction
import os
import sys
from .presets_manager import PresetManager


PREDEFINED_COLUMN_PRESETS = {
    "Stats sans KPI": ["campaign_name", "leveler","objective","kpi","view", "clic", "reach", "purchase", "purchase_value", "add_to_cart", "add_to_cart_value"],
    "Vue complète": [
        "id", "campaign_name", "goal", "status", "start_date", "end_date",
        "budget_margin", "spend_margin", "kpi_margin",
        "impressions", "clicks", "purchases", "cart_adds"
    ],
    "Stats uniquement": ["impressions", "clicks", "purchases", "value_purchases"]
}



class CampaignsTableWidget(QWidget):
    def __init__(self, font_manager, controller):
        super().__init__()

        self.font_manager = font_manager
        self.controller = controller
        self.preset_manager = PresetManager()


        # Raccourci Cmd+C (macOS)
        shortcut_copy = QShortcut(QKeySequence("Ctrl+C"), self)
        shortcut_copy.activated.connect(self.copy_selected_row_to_clipboard)

        shortcut_copy_mac = QShortcut(QKeySequence("Meta+C"), self)  # macOS
        shortcut_copy_mac.activated.connect(self.copy_selected_row_to_clipboard)
        # Création du modèle avec colonnes
        
        # self.model.setHorizontalHeaderLabels([
        #     "ID", "Nom de la campagne", "Levier", "Objectif", 
        #     "Budget margé", "Budget non margé", "State", "Start Date", 
        #     "End Date", "ID Admin account", "ID Ad Account", "ID Campaign"
        # ])
        self.campaign_columns_index = {
            "id": 0,
            "campaign_name": 1,
            "leveler": 2,
            "objective": 3, 
            "kpi": 4,
            "total_budget": 5,
            "nomarginbudget": 6,
            "state": 7,
            "start_date": 8,
            "end_date": 9,
            "public_id": 10,
            "campaign_public_name": 11,
            "ID_adminadaccount": 12,
            "ID_AdAccount": 13, 
            "ID_Campaign": 14,
            "spent_budget": 15,
            "impression": 16,
            "view": 17,
            "clic": 18,
            "reach": 19, 
            "purchase": 20,
            "purchase_value": 21,
            "add_to_cart": 22,
            "add_to_cart_value": 23,
            "kpi_with_margin": 24,
            "kpi_no_margin": 25,
            "spent_budget_margin": 26,
            "objective_target": 27,

            # ➕ KPIs margés
            "cpc_margin": 28,
            "cpm_margin": 29,
            "cpv_margin": 30,
            "cpa_margin": 31,

            # ➖ KPIs non margés
            "cpc_no_margin": 32,
            "cpm_no_margin": 33,
            "cpv_no_margin": 34,
            "cpa_no_margin": 35,

            # ➕ Autres KPIs
            "repetition": 36,
            "ctr": 37,
            "vtr": 38,

            # ➕ ROAS margé / non margé
            "roas_margin": 39,
            "roas_no_margin": 40,
            # Finnciaire
            "marge" : 41,
            "benefices_total" : 42
        }
        self.column_display_names = {
            "id": "ID",
            "campaign_name": "Nom de la campagne",
            "leveler": "Levier",
            "objective": "Objectif",
            "kpi": "KPI",
            "total_budget": "Budget margé",
            "nomarginbudget": "Budget non margé",
            "state": "État",
            "start_date": "Date de début",
            "end_date": "Date de fin",
            "public_id": "ID public",
            "campaign_public_name": "Nom public",
            "ID_adminadaccount": "Compte Business",
            "ID_AdAccount": "Compte Ads",
            "ID_Campaign": "ID Campagne",
            "spent_budget": "Dépenses (non margé)",
            "impression": "Impressions",
            "view": "Vues",
            "clic": "Clics",
            "reach": "Portée",
            "purchase": "Achats",
            "purchase_value": "Valeur des achats",
            "add_to_cart": "Ajouts au panier",
            "add_to_cart_value": "Valeur des ajouts",
            "kpi_with_margin": "KPI margé",
            "kpi_no_margin": "KPI non margé",
            "spent_budget_margin": "Dépenses margées",
            "objective_target": "Objectif prévisionnel",

            # ➕ KPIs margés
            "cpc_margin": "CPC margé",
            "cpm_margin": "CPM margé",
            "cpv_margin": "CPV margé",
            "cpa_margin": "CPA margé",

            # ➖ KPIs non margés
            "cpc_no_margin": "CPC non margé",
            "cpm_no_margin": "CPM non margé",
            "cpv_no_margin": "CPV non margé",
            "cpa_no_margin": "CPA non margé",

            # ➕ Autres KPIs
            "repetition": "Répétition",
            "ctr": "CTR (%)",
            "vtr": "VTR (%)",

            # ➕ ROAS
            "roas_margin": "ROAS margé",
            "roas_no_margin": "ROAS non margé",
            "marge": "Marge",
            # Finnciaire
            "marge" : "Marge",
            "benefices_total" : "Bénéfices Total"
        }
        self.column_display_widths = {
            "id": 35,
            "campaign_name": 370,
            "leveler": 80,
            "objective": 80, 
            "kpi": 80,
            "total_budget": 90,
            "nomarginbudget": 90,
            "state": 90,
            "start_date": 90,
            "end_date": 90,
            "public_id": 90,
            "campaign_public_name": 90,
            "ID_adminadaccount": 90,
            "ID_AdAccount": 90, 
            "ID_Campaign": 90,
            "spent_budget": 90,
            "impression": 90,
            "view": 90,
            "clic": 90,
            "reach": 90, 
            "purchase": 90,
            "purchase_value": 90,
            "add_to_cart": 90,
            "add_to_cart_value": 90,
            "kpi_with_margin": 90,
            "kpi_no_margin": 90,
            "spent_budget_margin": 90,
            "objective_target": 90,
        
            # ➕ KPIs margés
            "cpc_margin": 90,
            "cpm_margin": 90,
            "cpv_margin": 90,
            "cpa_margin": 90,

            # ➖ KPIs non margés
            "cpc_no_margin": 90,
            "cpm_no_margin": 90,
            "cpv_no_margin": 90,
            "cpa_no_margin": 90,

            # ➕ Autres KPIs
            "repetition": 90,
            "ctr": 90,
            "vtr": 90,

            # ➕ ROAS margé / non margé
            "roas_margin": 90,
            "roas_no_margin": 90,

            # Financiaire
            "marge": 90,
            "benefices_total": 90
        }
        # Définition des catégories de colonnes
        self.column_categories = {
            "attributs": [
                "id", "campaign_name", "leveler", "objective", "state", "start_date", "end_date"
            ],
            "parametres": [
                "public_id", "campaign_public_name", "ID_adminadaccount", "ID_AdAccount", "ID_Campaign"
            ],
            "stats_marge": [
                "total_budget", "spent_budget_margin", "kpi_with_margin"
            ],
            "stats_non_marge": [
                "nomarginbudget", "spent_budget", "kpi_no_margin"
            ],
            "kpi": ["kpi"],
            "stats": [
                "impression", "view", "clic", "reach", "purchase", "purchase_value",
                "add_to_cart", "add_to_cart_value"
            ],
            "previsions": ["objective_target"],

            # ➕ Nouveaux KPIs margés
            "kpi_marge": [
                "cpc_margin", "cpm_margin", "cpv_margin", "cpa_margin"
            ],

            # ➖ Nouveaux KPIs non margés
            "kpi_no_marge": [
                "cpc_no_margin", "cpm_no_margin", "cpv_no_margin", "cpa_no_margin"
            ],

            # ➕ Ratios & performances avancées
            "ratios": [
                "repetition", "ctr", "vtr", "roas_margin", "roas_no_margin"
            ],
            # Financiaire
            "financiaire": [
                "marge", "benefices_total"
            ]

        }
        # Définition des couleurs par catégorie
        self.category_colors = {
            "attributs": "",            # AliceBlue
            "parametres": "",           # Beige
            "stats_marge": "#002522",   # LightCyan
            "stats_non_marge": "#230300",  # MistyRose
            "kpi": "",                  # HoneyDew
            "stats": "#9E95FF",         # LavenderBlush
            "previsions": "#1F000A",    # LavenderBlush

            # Nouvelles catégories sans couleur
            "kpi_marge": "",
            "kpi_no_marge": "",
            "ratios": "",
            "financiaire": ""
        }
        self.category_text_colors = {
            "attributs": "#FFFFFF",         # AliceBlue
            "parametres": "#FFFFFF",        # Beige
            "stats_marge": "#FFFFFF",       # LightCyan
            "stats_non_marge": "#FFFFFF",   # MistyRose
            "kpi": "#FFFFFF",               # HoneyDew
            "stats": "#000000",             # LavenderBlush
            "previsions": "#FFFFFF",        # LavenderBlush

            # Nouvelles catégories sans couleur
            "kpi_marge": "#FFFFFF",
            "kpi_no_marge": "#FFFFFF",
            "ratios": "#FFFFFF",
            "financiaire": "#FFFFFF"
        }

        self.model = QStandardItemModel(0, len(self.campaign_columns_index))  # 0 lignes, 12 colonnes

        
        # Génère les labels dans l'ordre des colonnes
        ordered_keys = sorted(self.campaign_columns_index, key=self.campaign_columns_index.get)
        headers = [self.column_display_names.get(key, key) for key in ordered_keys]
        self.model.setColumnCount(len(headers))
        self.model.setHorizontalHeaderLabels(headers)


        self.locked_columns = [0]  # Colonnes verrouillées (ID)
        self.modified_rows = set()  # Stocke les lignes modifiées

        # Création du tableau
        self.table = QTableView()
        self.table.setSortingEnabled(True)  # Active le tri au clic
        self.table.setModel(self.model)

        # Activer la sélection de toute la ligne
        self.table.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.table.setSelectionMode(QTableView.SelectionMode.SingleSelection)
        
        # Définir le mode de redimensionnement par défaut pour toutes les colonnes
        self.table.horizontalHeader().setDefaultSectionSize(100)
        self.table.horizontalHeader().setStretchLastSection(True)  # Étire la dernière colonne

        self.table.horizontalHeader().setSectionsMovable(True)
        
        self.totals_table = QTableView()
        self.totals_table.setFixedHeight(40)
        self.totals_table.verticalHeader().setFixedWidth(30)
        self.totals_table.setEditTriggers(QTableView.EditTrigger.NoEditTriggers)
        self.totals_table.setSelectionMode(QTableView.SelectionMode.NoSelection)
        self.totals_table.setFocusPolicy(Qt.FocusPolicy.NoFocus)


        self.totals_model = QStandardItemModel(1, len(self.campaign_columns_index))
        self.totals_table.setModel(self.totals_model)
        self.totals_table.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.totals_table.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.totals_table.horizontalHeader().hide()
        self.totals_table.setEditTriggers(QTableView.EditTrigger.NoEditTriggers)


        # Synchronise explicitement le redimensionnement manuel
        self.table.horizontalHeader().sectionResized.connect(
            lambda index, _, new_size: self.totals_table.setColumnWidth(index, new_size)
        )
        self.table.horizontalScrollBar().valueChanged.connect(
            self.totals_table.horizontalScrollBar().setValue
        )
        # Quand on scroll sur le tableau des totaux (via molette ou touche flèche)
        self.totals_table.horizontalScrollBar().valueChanged.connect(
            self.table.horizontalScrollBar().setValue
        )
        self.table.horizontalHeader().sectionMoved.connect(self.sync_totals_column_order)
        





        # Définir des largeurs spécifiques pour chaque colonne
        # Appliquer les largeurs de colonnes basées sur column_display_weight
        for column_name, index in self.campaign_columns_index.items():
            if column_name in self.column_display_widths:
                width = self.column_display_widths[column_name]
                self.table.setColumnWidth(index, width)
                self.table.horizontalHeader().setSectionResizeMode(index, QHeaderView.ResizeMode.Interactive)
                self.totals_table.setColumnWidth(index, width)
                self.totals_table.horizontalHeader().setSectionResizeMode(index, QHeaderView.ResizeMode.Interactive)


                # Appliquer les couleurs de fond aux colonnes en fonction de leur catégorie
        



        self.table.setStyleSheet("""
            QTableView::item:selected {
                background-color: #476AFF;
                color: white;
            }
        """)

        # Connecter le signal pour détecter les modifications

        # Layout principal
        mainlayout = QVBoxLayout()

        # Add buttons for "Creer", "Modifier", "Supprimer"
        button_layout = QHBoxLayout()
        button_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)


        self.modify_button = QPushButton("Modifier")
        self.modify_button.setMaximumWidth(100)
        self.modify_button.setEnabled(False)  # Activé quand une ligne est sélectionnée

        self.view_button = QPushButton("View")
        self.view_button.setMaximumWidth(100)
        self.view_button.setEnabled(False)  # Activé quand une ligne est sélectionnée


        self.columns_menu = QMenu(self)
        self.columns_button = QPushButton("Colonnes")
        self.columns_button.setMenu(self.columns_menu)





        widget = ColumnSelectionWidget(
            column_display_names=self.column_display_names,
            column_categories=self.column_categories,
            campaign_columns_index=self.campaign_columns_index,
            on_change_callback=self.toggle_column_visibility,
            presets=PREDEFINED_COLUMN_PRESETS  # méthode à définir
        )

        widget_action = QWidgetAction(self.columns_menu)
        widget_action.setDefaultWidget(widget)
        self.columns_menu.addAction(widget_action)

        
        self.presets_menu = QMenu()

        # Ajoute dynamiquement les presets disponibles
        for name in self.preset_manager.get_all_preset_names():
            action = QAction(name, self)
            action.triggered.connect(lambda checked, n=name: self.apply_saved_preset(n))
            self.presets_menu.addAction(action)

        

        self.presets_button = QPushButton("Preset : Aucun preset")
        self.presets_menu = QMenu()
        self.presets_button.setMenu(self.presets_menu)
        self.apply_saved_preset("Aucun preset")
        self.refresh_presets_menu()
        
        
        

        #Sauvegarde de Presets
        self.save_preset_button = QPushButton("Sauvegarder preset")
        self.save_preset_button.setMaximumWidth(150)
        self.save_preset_button.clicked.connect(self.save_current_preset)
       
        


        button_layout.addWidget(self.modify_button)
        button_layout.addWidget(self.view_button)
        button_layout.addWidget(self.columns_button)
        button_layout.addWidget(self.presets_button)
        button_layout.addWidget(self.save_preset_button)
        
        mainlayout.addLayout(button_layout)
        mainlayout.addWidget(self.table)
        mainlayout.addWidget(self.totals_table)

        self.setLayout(mainlayout)

        # Connecter la sélection de ligne pour activer/désactiver les boutons
        self.table.selectionModel().selectionChanged.connect(self.on_selection_changed)

    def on_selection_changed(self, selected, deselected):
        """Active ou désactive les boutons en fonction de la sélection"""
        has_selection = len(self.table.selectionModel().selectedRows()) > 0
        self.modify_button.setEnabled(has_selection)
        self.view_button.setEnabled(has_selection)
        

    def update_data(self, data):
        """Met à jour les données du tableau en gardant les colonnes verrouillées"""
        self.model.removeRows(0, self.model.rowCount())  # Supprime les anciennes lignes

        for row, values in enumerate(data):
            for col, value in enumerate(values):
                item = QStandardItem(str(value))
                if isinstance(value, int):  # Permet un tri correct des nombres
                    item.setData(value, Qt.ItemDataRole.EditRole)

                # Désactiver l'édition pour les colonnes verrouillées
                if col in self.locked_columns:
                    item.setFlags(item.flags() & ~Qt.ItemFlag.ItemIsEditable)

                self.model.setItem(row, col, item)

        


    def get_table(self):
        """Retourne le QTableView pour être ajouté ailleurs"""
        return self.table

    def open_modify_campaign_window(self):
        """Ouvre une nouvelle fenêtre pour modifier une campagne"""
        selection_model = self.table.selectionModel()
        selected_rows = selection_model.selectedRows()
        if not selected_rows:
            QMessageBox.warning(self, "Erreur", "Veuillez sélectionner une campagne.")
            return

        selected_index = selected_rows[0]
        campaign_id = int(self.model.item(selected_index.row(), 0).text())
        QMessageBox.information(self, "Information", f"Fonctionnalité à implémenter: Modifier la campagne {campaign_id}")

    def open_view_campaign_window(self):
        """Ouvre une nouvelle fenêtre pour voir une campagne"""
        selection_model = self.table.selectionModel()
        selected_rows = selection_model.selectedRows()
        if not selected_rows:
            QMessageBox.warning(self, "Erreur", "Veuillez sélectionner une campagne.")
            return

        selected_index = selected_rows[0]
        campaign_id = int(self.model.item(selected_index.row(), 0).text())
        QMessageBox.information(self, "Information", f"Fonctionnalité à implémenter: Voir la campagne {campaign_id}")
    
    
    def apply_colors(self):
        """Applique les couleurs de fond aux en-têtes de colonnes selon leur catégorie"""
        for category, columns in self.column_categories.items():
            color = QColor(self.category_colors.get(category, "#FFFFFF"))
            color_texte = QColor(self.category_text_colors.get(category, "#FFFFFF"))
            for column_name in columns:
                if column_name in self.campaign_columns_index:
                    col_index = self.campaign_columns_index[column_name]
                    for row in range(self.model.rowCount()):
                        item = self.model.item(row, col_index)
                        if item is not None:
                            item.setBackground(color)
                            item.setForeground(color_texte)
        
        # Colorer la cellule State en fonction de sa valeur
        state_column_index = self.campaign_columns_index['state']
        for row in range(self.model.rowCount()):
            item = self.model.item(row, state_column_index)
            if item:
                state_value = str(item.text()).lower()
                if state_value == "active":
                    item.setBackground(QColor("#4CAF50"))  # Vert
                    item.setForeground(QColor("white"))
                elif state_value == "waiting":
                    item.setBackground(QColor("#FF9800"))  # Orange
                    item.setForeground(QColor("white"))
                elif state_value == "rejected":
                    item.setBackground(QColor("#F44336"))  # Rouge
                    item.setForeground(QColor("white"))
                elif state_value == "paused":
                    item.setBackground(QColor("#2196F3"))  # Bleu
                    item.setForeground(QColor("white"))
                elif state_value == "ended":
                    item.setBackground(QColor("#9E9E9E"))  # Gris
                    item.setForeground(QColor("white"))

    def toggle_column_visibility(self, index: int, visible: bool):
        
        if visible:
            self.table.showColumn(index)
        else:
            self.table.hideColumn(index)


    def copy_selected_row_to_clipboard(self):
        indexes = self.table.selectionModel().selectedRows()
        if not indexes:
            return

        # Prend la première ligne sélectionnée
        row = indexes[0].row()

        # Récupère les headers visibles
        headers = []
        for col in range(self.model.columnCount()):
            if not self.table.isColumnHidden(col):
                headers.append(self.model.headerData(col, Qt.Orientation.Horizontal))

        # Récupère les valeurs de la ligne visible
        row_values = []
        for col in range(self.model.columnCount()):
            if not self.table.isColumnHidden(col):
                item = self.model.item(row, col)
                row_values.append(item.text() if item else "")

        # Format pour Excel : tabulation entre colonnes, saut de ligne à la fin
        output = "\t".join(headers) + "\n" + "\t".join(row_values)

        QApplication.clipboard().setText(output)

    @staticmethod
    def format_number_french_style(value, suffix="", decimals=2):
        """
        Formate un nombre à la française :
        - espaces tous les 3 chiffres
        - virgule comme séparateur décimal
        - suffixe optionnel (€, %)
        - supprime la virgule si `decimals` vaut 0
        """
        try:
            number = float(value)
        except (ValueError, TypeError):
            return str(value)

        rounded = round(number, decimals)

        if decimals == 0:
            formatted_integer = f"{int(rounded):,}".replace(",", " ")
            return f"{formatted_integer} {suffix}".strip()
        else:
            parts = f"{rounded:.{decimals}f}".split(".")
            integer_part = parts[0]
            decimal_part = parts[1]

            reversed_digits = integer_part[::-1]
            grouped = " ".join(reversed_digits[i:i+3] for i in range(0, len(reversed_digits), 3))
            formatted_integer = grouped[::-1]

            return f"{formatted_integer},{decimal_part} {suffix}".strip()



    def update_totals(self):
        print("→ update_totals called")

        euro_columns = {
            "total_budget", "nomarginbudget", "spent_budget",
            "spent_budget_margin", "purchase_value", "add_to_cart_value", "benefices_total"
        }

        percent_columns = {
            "ctr", "vtr", "marge"
        }

        numeric_columns = [
            "total_budget", "nomarginbudget", "spent_budget", "impression", "view", "clic",
            "reach", "purchase", "purchase_value", "add_to_cart", "add_to_cart_value",
            "spent_budget_margin", "benefices_total", "ctr", "vtr",
            "roas_margin", "roas_no_margin", "marge", "repetition",
            "cpc_margin", "cpv_margin", "cpm_margin", "cpa_margin",
            "cpc_no_margin", "cpv_no_margin", "cpm_no_margin", "cpa_no_margin"
        ]

        column_decimals = {
            "clic": 0, "view": 0, "impression": 0, "reach": 0, "purchase": 0,
            "add_to_cart": 0, "purchase_value": 2, "add_to_cart_value": 2,
            "benefices_total": 2, "spent_budget": 2, "spent_budget_margin": 2,
            "total_budget": 2, "nomarginbudget": 2, "ctr": 2, "vtr": 2,
            "roas_margin": 2, "roas_no_margin": 2, "marge": 1, "repetition": 2,

            # ➕ KPIs margés
            "cpc_margin": 3, "cpv_margin": 3, "cpm_margin": 3, "cpa_margin": 3,

            # ➖ KPIs non margés
            "cpc_no_margin": 3, "cpv_no_margin": 3, "cpm_no_margin": 3, "cpa_no_margin": 3
        }

        calculated_columns = {
            "ctr": ("clic", "impression"),
            "vtr": ("view", "impression"),
            "roas_margin": ("purchase_value", "spent_budget_margin"),
            "roas_no_margin": ("purchase_value", "spent_budget"),
            "marge": ("nomarginbudget", "total_budget"),
            "repetition": ("impression", "reach"),

            # ➕ KPIs margés
            "cpc_margin": ("spent_budget_margin", "clic"),
            "cpv_margin": ("spent_budget_margin", "view"),
            "cpm_margin": ("spent_budget_margin", "impression"),
            "cpa_margin": ("spent_budget_margin", "purchase"),

            # ➖ KPIs non margés
            "cpc_no_margin": ("spent_budget", "clic"),
            "cpv_no_margin": ("spent_budget", "view"),
            "cpm_no_margin": ("spent_budget", "impression"),
            "cpa_no_margin": ("spent_budget", "purchase")
        }

        sums = {col: 0.0 for col in numeric_columns}

        for row in range(self.model.rowCount()):
            if not self.table.isRowHidden(row):
                for col_name in numeric_columns:
                    col_index = self.campaign_columns_index.get(col_name)
                    if col_index is not None:
                        item = self.model.item(row, col_index)
                        if item:
                            try:
                                text = item.text().replace(" ", "").replace("€", "").replace("%", "").replace(",", ".")
                                sums[col_name] += float(text)
                            except ValueError:
                                pass

        # Appliquer les formules des colonnes calculées
        for target_col, (numerator, denominator) in calculated_columns.items():
            num = sums.get(numerator, 0.0)
            denom = sums.get(denominator, 0.0)
            if target_col == "marge":
                if denom != 0:
                    sums[target_col] = 1 - (num / denom)
                else:
                    sums[target_col] = 0.0
            else:
                sums[target_col] = (num / denom) if denom else 0.0

        # Affichage final
        for col_name, col_index in self.campaign_columns_index.items():
            if col_name in numeric_columns:
                value = sums.get(col_name, 0.0)
                decimals = column_decimals.get(col_name, 2)
                suffix = ""
                if col_name in euro_columns:
                    suffix = "€"
                elif col_name in percent_columns:
                    value *= 100
                    suffix = "%"

                formatted = self.format_number_french_style(value, suffix=suffix, decimals=decimals)
                item = QStandardItem(formatted)
                item.setBackground(QColor("#476AFF"))
                item.setForeground(QColor("white"))
                item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
                self.totals_model.setItem(0, col_index, item)
            else:
                self.totals_model.setItem(0, col_index, QStandardItem(""))

        self.totals_model.setItem(0, 1, QStandardItem("Total"))


    def sync_totals_column_order(self):
        """Applique l’ordre des colonnes de la table principale à la table des totaux."""
        header = self.table.horizontalHeader()
        for visual_index in range(header.count()):
            logical_index = header.logicalIndex(visual_index)
            self.totals_table.horizontalHeader().moveSection(
                self.totals_table.horizontalHeader().visualIndex(logical_index),
                visual_index
            )


    def save_current_preset(self):
        

        header = self.table.horizontalHeader()
        order = [header.logicalIndex(i) for i in range(header.count())]
        visibility = {
            col_name: not self.table.isColumnHidden(index)
            for col_name, index in self.campaign_columns_index.items()
        }

        preset_name, ok = QInputDialog.getText(self, "Nom du preset", "Nom du nouveau preset :")
        if ok and preset_name:
            self.preset_manager.save_preset(preset_name, {"order": order, "visibility": visibility})

        self.refresh_presets_menu()

    def refresh_presets_menu(self):
        self.presets_menu.clear()

        # Fonction utilitaire pour créer une ligne
        def create_preset_row(name, is_default=False):
            frame = QFrame()
            frame.setFrameShape(QFrame.Shape.NoFrame)
            frame.setStyleSheet("""
                QFrame {
                    padding: 2px;
                }
                QFrame:hover {
                    background-color: #476AFF;
                }
            """)
            layout = QHBoxLayout()
            layout.setContentsMargins(8, 4, 8, 4)
            layout.setSpacing(6)

            # Nom du preset (clic sur toute la zone de texte)
            label = QLabel(name)
            label.setStyleSheet("QLabel { font-size: 13px; }")
            label.setCursor(Qt.CursorShape.PointingHandCursor)

            # Rendre le frame entier cliquable
            def handle_frame_click(event):
                self.presets_menu.hide()
                self.apply_saved_preset(name)
                
            frame.mousePressEvent = handle_frame_click

            # Le label n'a plus besoin de gérer les clics
            layout.addWidget(label, 1)  # stretch pour prendre toute la place

            # Bouton supprimer (sauf pour "Aucun preset")
            if not is_default:
                delete_btn = QPushButton()
                delete_btn.setIcon(QIcon.fromTheme("edit-delete") or QIcon("icons/trash.png"))
                delete_btn.setFixedSize(20, 20)
                delete_btn.setStyleSheet("QPushButton { border: none; }")
                delete_btn.setCursor(Qt.CursorShape.PointingHandCursor)
                
                # Empêcher la propagation du clic au frame parent
                def handle_delete_click(checked=False, n=name):
                    # Arrêter la propagation de l'événement
                    self.confirm_delete_preset(n)
                    return True
                    
                delete_btn.clicked.connect(handle_delete_click)
                layout.addWidget(delete_btn)
            else:
                layout.addSpacing(20)  # Réserve la place pour l'icône invisible

            frame.setLayout(layout)
            return frame

        # ➕ Ajout de "Aucun preset"
        action_default = QWidgetAction(self)
        action_default.setDefaultWidget(create_preset_row("Aucun preset", is_default=True))
        self.presets_menu.addAction(action_default)

        # ➕ Ajout des presets sauvegardés
        for name in self.preset_manager.get_all_preset_names():
            action = QWidgetAction(self)
            action.setDefaultWidget(create_preset_row(name))
            self.presets_menu.addAction(action)

        # Mise à jour du bouton si aucun preset
        if not self.preset_manager.get_all_preset_names():
            self.presets_button.setText("Presets")

        self.presets_button.adjustSize()


    def confirm_delete_preset(self, name):
        reply = QMessageBox.question(
            self,
            "Supprimer le preset",
            f"Voulez-vous vraiment supprimer le preset '{name}' ?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.preset_manager.delete_preset(name)
            self.refresh_presets_menu()

    
    def apply_saved_preset(self, name):
        if name == "Aucun preset":
            for col_name, index in self.campaign_columns_index.items():
                self.table.showColumn(index)
            self.presets_button.setText("Preset : Aucun preset")
            return

        preset = self.preset_manager.get_preset(name)
        if not preset:
            return

        # Compatibilité : récupérer colonnes visibles
        visible_columns = [
            col for col, is_visible in preset.get("visibility", {}).items() if is_visible
        ]

        # Récupérer l'ordre des colonnes - correction de la ligne problématique
        column_order = None
        if "order" in preset:
            column_order = preset["order"]
        elif "column_order" in preset:
            column_order = preset["column_order"]

        # Appliquer colonnes visibles
        for col_name, index in self.campaign_columns_index.items():
            self.table.setColumnHidden(index, col_name not in visible_columns)

        # Appliquer l'ordre
        if column_order:
            for i, logical_index in enumerate(column_order):
                visual_index = self.table.horizontalHeader().visualIndex(logical_index)
                if visual_index >= 0:
                    self.table.horizontalHeader().moveSection(visual_index, i)

        # Synchroniser la table des totaux
        self.sync_totals_column_order()
        
        self.presets_button.setText(f"Preset : {name}")



class CampaignFiltersWidget(QWidget):
    def __init__(self, campaigns_table_widget):
        super().__init__()
        self.campaigns_table_widget = campaigns_table_widget
        self.layout = QHBoxLayout()
        self.layout.setSpacing(10)

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Rechercher une campagne")
        self.layout.addWidget(self.search_input)

        self.levier_filter = QComboBox()
        self.levier_filter.addItem("Levier")
        self.layout.addWidget(self.levier_filter)

        self.objectif_filter = QComboBox()
        self.objectif_filter.addItem("Objectif")
        self.layout.addWidget(self.objectif_filter)

        self.status_filter = QComboBox()
        self.status_filter.addItem("Statut")
        self.layout.addWidget(self.status_filter)

        self.business_filter = QComboBox()
        self.business_filter.addItem("Compte Business")
        self.layout.addWidget(self.business_filter)

        self.adaccount_filter = QComboBox()
        self.adaccount_filter.addItem("Compte Ads")
        self.layout.addWidget(self.adaccount_filter)

        self.start_date_filter = QDateEdit()
        self.start_date_filter.setCalendarPopup(True)
        self.start_date_filter.setButtonSymbols(QDateEdit.ButtonSymbols.NoButtons)
        self.start_date_filter.setDisplayFormat("yyyy-MM-dd")
        self.start_date_filter.setSpecialValueText("Date de début")
        self.start_date_filter.setDate(QDate.currentDate())
        self.layout.addWidget(self.start_date_filter)
        self.search_input.setStyleSheet("""
            QLineEdit {
                outline: none;
                border: 1px solid gray;
                border-radius: 5px;
                padding: 2px;
            }
            QLineEdit:focus {
                border: 1px solid #888;
            }
        """)
        self.start_date_filter.setStyleSheet("""
            QDateEdit {
                outline: none;
                border: 1px solid gray;
                border-radius: 5px;
                padding: 2px;
            }
            QDateEdit:focus {
                border: 1px solid #888;
            }

            QCalendarWidget QWidget {
                background-color: white;
                color: black;

            }

            QCalendarWidget QToolButton {
                background-color: none;
                color: black;
                font-weight: bold;
                border: none;
                margin: 5px;
            }

            QCalendarWidget QToolButton:hover {
                background-color: #eeeeee;
            }

            QCalendarWidget QSpinBox {
                width: 70px;
                font-size: 12px;
                color: black;
                background-color: none;
                border: none;
            }

            QCalendarWidget QMenu {
                background-color: white;
                border: 1px solid gray;
            }

            QCalendarWidget QAbstractItemView {
                selection-background-color: #476AFF;
                selection-color: white;
                background-color: #fefefe;
                gridline-color: lightgray;
            }

            QCalendarWidget QHeaderView::section {
                background-color: #476AFF;
                color: white;
                font-weight: bold;
            }

            QCalendarWidget QAbstractItemView::item {
                color: black;
            }

            QCalendarWidget QAbstractItemView::item:enabled:nth-child(7),  /* Saturday */
            QCalendarWidget QAbstractItemView::item:enabled:nth-child(1)   /* Sunday */
            {
                color: #476AFF;
            }
            QCalendarWidget QToolButton#qt_calendar_prevmonth {
                qproperty-icon: url("assets/icons/arrow-circle-left.png");
                qproperty-iconSize: 20px 20px;
            }

            QCalendarWidget QToolButton#qt_calendar_nextmonth {
                qproperty-icon: url("assets/icons/arrow-circle-right.png");
                qproperty-iconSize: 20px 20px;
            }


        """)
        self.start_date_filter.lineEdit().setReadOnly(True)
        self.start_date_filter.lineEdit().setCursor(Qt.CursorShape.ArrowCursor)
        self.reset_button = QPushButton("Réinitialiser")
        self.reset_button.clicked.connect(self.reset_filters)
        self.layout.addWidget(self.reset_button)
        self.setLayout(self.layout)

        self.search_input.textChanged.connect(self.filter_table)
        self.start_date_filter.dateChanged.connect(self.filter_table)
        self.levier_filter.currentIndexChanged.connect(self.filter_table)
        self.objectif_filter.currentIndexChanged.connect(self.filter_table)
        self.status_filter.currentIndexChanged.connect(self.filter_table)
        self.business_filter.currentIndexChanged.connect(self.filter_table)


        #Ad Account filter init
        # self.adaccount_col = 0
        self.business_filter.currentIndexChanged.connect(lambda: self._populate_filter_visible(self.adaccount_filter, campaigns_table_widget.model, self.adaccount_col, 'Ad Account'))
        self.business_filter.currentIndexChanged.connect(lambda: self.adaccount_filter.setCurrentIndex(0))
        self.adaccount_filter.currentIndexChanged.connect(self.filter_table)

    def update_filters_from_table(self, model: QStandardItemModel):
        self.levier_col = self.campaigns_table_widget.campaign_columns_index['leveler']
        self.objectif_col = self.campaigns_table_widget.campaign_columns_index['objective']
        self.status_col = self.campaigns_table_widget.campaign_columns_index['state']
        self.business_col = self.campaigns_table_widget.campaign_columns_index['ID_adminadaccount']
        self.adaccount_col = self.campaigns_table_widget.campaign_columns_index['ID_AdAccount']
        

        self.name_col = self.campaigns_table_widget.campaign_columns_index['campaign_name']
        self.start_date_col = self.campaigns_table_widget.campaign_columns_index['start_date']
        
        self._populate_filter(self.levier_filter, model, self.levier_col, "Levier")
        self._populate_filter(self.objectif_filter, model, self.objectif_col, 'Objectif')
        self._populate_filter(self.status_filter, model, self.status_col, 'Statut')
        self._populate_filter(self.business_filter, model, self.business_col, 'Business Account')
        self._populate_filter(self.adaccount_filter, model, self.adaccount_col, 'Ad Account')

    def _populate_filter(self, combo: QComboBox, model: QStandardItemModel, column: int,text):
        existing_values = set()
        for row in range(model.rowCount()):
            value = model.item(row, column).text()
            if value not in existing_values:
                existing_values.add(value)
        combo.clear()
        combo.addItem(text)
        combo.addItems(sorted(existing_values))

    def _populate_filter_visible(self, combo: QComboBox, model: QStandardItemModel, column: int, text):
        existing_values = set()
        table = self.campaigns_table_widget.get_table()
        for row in range(model.rowCount()):
            if not table.isRowHidden(row):
                value = model.item(row, column).text()
                if value not in existing_values:
                    existing_values.add(value)
        combo.clear()
        combo.addItem(text)
        combo.addItems(sorted(existing_values))

    def get_layout(self):
        return self.layout

    def filter_table(self):

        
        model = self.campaigns_table_widget.model
        search_text = self.search_input.text().lower()
        selected_start_date = self.start_date_filter.date().toString("yyyy-MM-dd")
        
        for row in range(model.rowCount()):
            show_row = True
            if self.levier_filter.currentIndex() > 0:
                val = model.item(row, self.levier_col).text()
                if val != self.levier_filter.currentText():
                    show_row = False
            if self.objectif_filter.currentIndex() > 0:
                val = model.item(row, self.objectif_col).text()
                if val != self.objectif_filter.currentText():
                    show_row = False
            if self.status_filter.currentIndex() > 0:
                val = model.item(row, self.status_col).text()
                if val != self.status_filter.currentText():
                    show_row = False
            if self.business_filter.currentIndex() > 0:
                val = model.item(row, self.business_col).text()
                if val != self.business_filter.currentText():
                    show_row = False
            
            if self.adaccount_filter.currentIndex() > 0:
                val = model.item(row, self.adaccount_col).text()
                if val != self.adaccount_filter.currentText():
                    show_row = False
            
            if search_text:
                campaign_name = model.item(row, self.name_col).text().lower()
                if search_text not in campaign_name:
                    show_row = False
            # Compare start date only if selected date is not today's date
            if self.start_date_filter.date() != QDate.currentDate():
                row_start_date = model.item(row, self.start_date_col).text()
                if row_start_date < selected_start_date:
                    show_row = False
            

            self.campaigns_table_widget.get_table().setRowHidden(row, not show_row)
        
        self.campaigns_table_widget.update_totals()

    def reset_filters(self):
        self.search_input.clear()
        self.levier_filter.setCurrentIndex(0)
        self.objectif_filter.setCurrentIndex(0)
        self.status_filter.setCurrentIndex(0)
        self.business_filter.setCurrentIndex(0)
        self.adaccount_filter.setCurrentIndex(0)
        self.start_date_filter.setDate(QDate.currentDate())



class ColumnSelectionWidget(QWidget):

    def __init__(self, column_display_names, column_categories, campaign_columns_index, on_change_callback, presets=None):
        super().__init__()
        self.checkboxes = {}
        self.campaign_columns_index = campaign_columns_index
        self.on_change_callback = on_change_callback
        self.presets = presets or {}

        main_layout = QVBoxLayout()
        self.setLayout(main_layout)

        # Boutons globaux
        btn_all = QPushButton("Tout afficher")
        btn_none = QPushButton("Tout masquer")
        btn_all.clicked.connect(self.select_all)
        btn_none.clicked.connect(self.deselect_all)
        main_layout.addWidget(btn_all)
        main_layout.addWidget(btn_none)

        # Colonnes pour catégories et presets
        columns_layout = QHBoxLayout()
        left_col = QVBoxLayout()
        right_col = QVBoxLayout()
        presets_col = QVBoxLayout()

        all_categories = list(column_categories.items())
        for i, (category, columns) in enumerate(all_categories):
            category_layout = QVBoxLayout()
            category_layout.addWidget(QLabel(f"— {category.capitalize()} —"))

            for col_name in columns:
                if col_name in column_display_names and col_name in campaign_columns_index:
                    checkbox = QCheckBox(column_display_names[col_name])
                    checkbox.setChecked(True)
                    checkbox.stateChanged.connect(self.make_checkbox_handler(col_name))
                    self.checkboxes[col_name] = checkbox
                    category_layout.addWidget(checkbox)

            if i % 2 == 0:
                left_col.addLayout(category_layout)
            else:
                right_col.addLayout(category_layout)

        # === Presets définis manuellement ===



        for label, cols in self.presets.items():
            btn = QPushButton(label)
            btn.clicked.connect(lambda _, c=cols: self.apply_preset(c))
            presets_col.addWidget(btn)
        
        # Assemblage des 3 colonnes
        columns_layout.addLayout(left_col)
        columns_layout.addLayout(right_col)
        columns_layout.addLayout(presets_col)
        main_layout.addLayout(columns_layout)



    def make_checkbox_handler(self, col_name):
        def handler(_):  # on ignore `state`
            checkbox = self.checkboxes[col_name]
            visible = checkbox.isChecked()
            index = self.campaign_columns_index[col_name]
            self.on_change_callback(index, visible)
        return handler


    def apply_preset(self, col_names):
        for name, checkbox in self.checkboxes.items():
            checkbox.setChecked(name in col_names)

    def select_all(self):
        for col_name, cb in self.checkboxes.items():
            if not cb.isChecked():
                cb.setChecked(True)
            else:
                # Forcer même si déjà coché
                index = self.campaign_columns_index[col_name]
                self.on_change_callback(index, True)

    def deselect_all(self):
        for col_name, cb in self.checkboxes.items():
            if cb.isChecked():
                cb.setChecked(False)
            else:
                index = self.campaign_columns_index[col_name]
                self.on_change_callback(index, False)
