"""
Widget pour la gestion des projets.
"""

from PyQt6.QtWidgets import <PERSON>Widget, QVBoxLayout, QLabel, QComboBox, QPushButton, QHBoxLayout, QFrame
from PyQt6.QtCore import Qt

from .project_dialogs import AddProjectDialog
from .account_widget import apply_rounded_mask

class ProjectWidget(QWidget):
    def __init__(self, font_manager):
        super().__init__()

        # Encapsulation dans un `QFrame`
        self.font_manager = font_manager
        self.frame = QFrame(self)
        self.frame.setFont(self.font_manager.get_font("regular", 12))
        self.frame.setObjectName("projectFrame")
        self.frame.setFixedSize(320, 90)  # Même taille que AccountWidget
        self.frame.setStyleSheet("""
            QFrame#projectFrame {
                border: 1px solid gray;
                border-radius: 10px;
            }
        """)

        # Layout principal horizontal
        self.project_main_layout = QHBoxLayout(self.frame)
        self.project_main_layout.setContentsMargins(10, 5, 10, 5)  # Marges internes
        self.project_main_layout.setSpacing(10)  # Espacement entre éléments

        # Layout vertical pour les infos du projet (label + dropdown)
        project_info_layout = QVBoxLayout()
        project_info_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)  # Aligner à gauche

        # Label pour sélectionner un projet
        self.label2 = QLabel("Selected project :")
        self.label2.setFont(self.font_manager.get_font("bold", 16))
        project_info_layout.addWidget(self.label2)

        # Dropdown pour choisir un projet
        self.projects_dropdown = QComboBox()
        self.projects_dropdown.setFixedSize(200, 40)
        self.projects_dropdown.setEnabled(False)  # Désactivé par défaut
        project_info_layout.addWidget(self.projects_dropdown)

        # Bouton pour ajouter un projet
        self.add_project_button = QPushButton("Add Project")
        self.add_project_button.setFixedSize(100, self.add_project_button.sizeHint().height())
        project_info_layout.addWidget(self.add_project_button)
        self.add_project_button.setEnabled(False)

        # Ajouter le layout vertical dans le layout horizontal
        self.project_main_layout.addLayout(project_info_layout)

        # Ajout de l'image du projet (à droite)
        self.project_image = QLabel(self)
        self.project_image.setFixedSize(70, 70)
        apply_rounded_mask(self.project_image, radius=15)
        self.project_image.setScaledContents(True)
        self.project_image.setEnabled(False)

        # Ajouter l'image à droite
        self.project_main_layout.addWidget(self.project_image, alignment=Qt.AlignmentFlag.AlignRight)

    def get_layout(self):
        """Retourne le `QFrame` pour être ajouté ailleurs"""
        return self.frame  # On retourne `self.frame` au lieu du layout

    def open_add_project_window(self, account_id_selected, account_name_selected, controller):
        """Ouvre une nouvelle fenêtre pour ajouter un projet"""
        dialog = AddProjectDialog(account_id_selected, account_name_selected, controller, self.font_manager)
        dialog.exec()  # Affiche la boîte de dialogue en mode bloquant
