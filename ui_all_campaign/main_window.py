"""
Fenêtre principale pour la vue de toutes les campagnes.
"""

from PyQt6.QtWidgets import QMain<PERSON>indow, QWidget, QVBoxLayout, QMessageBox
from PyQt6.QtCore import Qt

from .campaigns_table import CampaignsTableWidget, CampaignFiltersWidget



class AllCampaignsWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        from controller import DataController  # Import ici pour éviter les imports circulaires
        self.controller = DataController(self)  # Contrôleur pour gérer les requêtes
        from tools.font_manager import FontManager
        self.font_manager = FontManager()
        self.init_ui()

    def init_ui(self):
        self.setWindowTitle("SA Corp - Toutes les Campagnes")
        self.setGeometry(100, 100, 1500, 700)
        self.setFont(self.font_manager.get_font("regular", 12))

        # Créer un widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Créer la barre de menu
        self.create_menu_bar()

        # Layout principal pour le widget central
        layout = QVBoxLayout(central_widget)

        # Tableau des campagnes et filtres
        self.campaigns_table = CampaignsTableWidget(self.font_manager, self.controller)
        self.campaignsfilter = CampaignFiltersWidget(self.campaigns_table)
        
        layout.addWidget(self.campaignsfilter)
        layout.addWidget(self.campaigns_table)

        # Connecter les boutons aux actions

        self.campaigns_table.modify_button.clicked.connect(self.campaigns_table.open_modify_campaign_window)
        self.campaigns_table.view_button.clicked.connect(self.campaigns_table.open_view_campaign_window)

        # Charger les données initiales
        self.load_all_campaigns()

    def create_menu_bar(self):
        """Crée la barre de menu de l'application."""
        menu_bar = self.menuBar()

        # Menu Fichier
        file_menu = menu_bar.addMenu("Fichier")

        # Action Retour au menu principal
        main_menu_action = file_menu.addAction("Retour au menu principal")
        main_menu_action.triggered.connect(self.return_to_main_menu)

        # Action Réinitialiser
        reset_action = file_menu.addAction("Réinitialiser l'application")
        reset_action.triggered.connect(self.reset_application)

        # Ajouter un séparateur
        file_menu.addSeparator()

        # Action Quitter
        quit_action = file_menu.addAction("Quitter")
        quit_action.triggered.connect(self.close)

    def load_all_campaigns(self):
        """Charge toutes les campagnes depuis la base de données."""
        

        self.controller.update_all_campaigns_options()

        self.campaigns_table.apply_colors()
        self.campaigns_table.update_totals()


    def reset_application(self):
        """Réinitialise l'application en rechargeant les données depuis la base de données."""
        # Demander confirmation à l'utilisateur
        reply = QMessageBox.question(self, "Confirmation", 
                                   "Voulez-vous vraiment réinitialiser l'application ? \n\nCela rechargera toutes les données depuis la base de données.", 
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        
        if reply == QMessageBox.StandardButton.Yes:
            # Vider le tableau
            self.campaigns_table.model.removeRows(0, self.campaigns_table.model.rowCount())
            
            # Réinitialiser les filtres
            self.campaignsfilter.reset_filters()
            
            # Recharger les données
            self.load_all_campaigns()
            
            # Afficher un message de confirmation
            QMessageBox.information(self, "Réinitialisation", "L'application a été réinitialisée avec succès.")

    def return_to_main_menu(self):
        """Retourne au menu principal de sélection d'outils sans demander de confirmation."""
        # Importer ici pour éviter les imports circulaires
        import main
        
        # Créer et afficher la fenêtre de sélection d'outils
        self.tool_selection = main.ToolSelectionWindow(self.font_manager)
        self.tool_selection.show()
        
        # Fermer la fenêtre actuelle
        self.close()

    def closeEvent(self, event):
        print("Fenêtre de toutes les campagnes fermée proprement")
        # Arrêter les timers, threads, sauvegarder l'état, etc.
        event.accept()

    def show_message(self, title, message):
        """Affiche un message à l'utilisateur."""
        QMessageBox.information(self, title, message)
