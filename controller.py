from model import DatabaseModel
import time
import requests
from PyQt6.QtGui import QFont, QPixmap, QColor

from PyQt6.QtNetwork import QNetworkAccessManager, QNetworkRequest
from PyQt6.QtCore import QUrl, QByteArray, pyqtSignal, QObject
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QStandardItemModel, QStandardItem

from datetime import datetime
import re


def convert_date_format(date_str):
    """Convertit une date du format YYYY-MM-DD au format DD/MM/YYYY."""
    if not date_str or len(date_str) != 10:
        return date_str
    try:
        year, month, day = date_str.split('-')
        return f"{day}/{month}/{year}"
    except:
        return date_str


class DataController:
    def __init__(self, ui):
        self.ui = ui  # Référence à l'interface
        self.model = DatabaseModel()  # Initialiser le modèle de base de données



    def load_initial_options(self):
        """Charge les options initiales pour le premier champ de sélection."""
        accounts_data = self.model.fetch_accounts()



        # Simuler un délai pour le chargement des options initiales


        for item_id, name, account_logo in accounts_data:
            self.ui.account_widget.accounts_dropdown.addItem(name, (item_id,account_logo))

        selected_id, account_logo_url = self.ui.account_widget.accounts_dropdown.currentData()
        self.ui.account_id_selected = selected_id
        self.ui.account_name_selected = self.ui.account_widget.accounts_dropdown.currentText()

        # Masquer l'indicateur de chargement après le chargement des options


    def update_account_options(self):
        """Charge les options initiales pour le premier champ de sélection."""
        accounts_data = self.model.fetch_accounts()
        self.ui.account_widget.accounts_dropdown.clear()


        # Simuler un délai pour le chargement des options initiales


        for item_id, name, account_logo in accounts_data:
            self.ui.account_widget.accounts_dropdown.addItem(name, (item_id,account_logo))

        selected_id, account_logo_url = self.ui.account_widget.accounts_dropdown.currentData()
        self.ui.account_id_selected = selected_id
        self.ui.account_name_selected = self.ui.account_widget.accounts_dropdown.currentText()

        # Masquer l'indicateur de chargement après le chargement des options


    def update_user_options(self,account_id,model):

        user_data = self.model.fetch_user(account_id)

        model.clear()
        model.setHorizontalHeaderLabels(["ID", "Email", "Name", "First Name", "Company", "Role"])

        for user_id, user_email, user_name, user_firstname, user_company, user_role in user_data:
            row = [
                QStandardItem(str(user_id)),
                QStandardItem(user_email),
                QStandardItem(user_name),
                QStandardItem(user_firstname),
                QStandardItem(user_company),
                QStandardItem(user_role),
            ]
            for item in row:
                item.setFlags(item.flags() & ~Qt.ItemFlag.ItemIsEditable)
            model.appendRow(row)


        return

    def update_projects_options(self):
        """Met à jour le deuxième champ en fonction de la sélection du premier."""
        selected_id, account_logo_url = self.ui.account_widget.accounts_dropdown.currentData()
        self.ui.group_campaign_table.model.removeRows(0, self.ui.group_campaign_table.model.rowCount())

        #sauvergarde des data selectionnées
        self.ui.account_id_selected = selected_id
        self.ui.account_name_selected = self.ui.account_widget.accounts_dropdown.currentText()

        self.ui.group_campaign_table.add_group_button.setEnabled(False)
        self.ui.group_campaign_table.delete_group_button.setEnabled(False)
        self.ui.group_campaign_table.modify_group_button.setEnabled(False)
        self.ui.campaigns_table.create_button.setEnabled(False)
        self.ui.campaigns_table.modify_button.setEnabled(False)
        self.ui.campaigns_table.view_button.setEnabled(False)
        self.ui.campaigns_table.delete_button.setEnabled(False)


        self.ui.project_widget.projects_dropdown.blockSignals(True)
        if selected_id:
            new_options = self.model.fetch_projects(selected_id)

            # Nettoyer et mettre à jour le deuxième menu
            self.ui.project_widget.projects_dropdown.clear()
            self.ui.project_widget.project_image.clear()
            self.ui.project_widget.project_image.setEnabled(False)

            self.ui.project_widget.projects_dropdown.addItem("Selectionnez le projet..", (-1,""))
            for item_id, name, project_logo_url in new_options:
                self.ui.project_widget.projects_dropdown.addItem(name, (item_id,project_logo_url))
        else:

            self.ui.project_widget.projects_dropdown.clear()
            self.ui.project_widget.project_image.clear()
            self.ui.project_widget.project_image.setEnabled(False)

        self.ui.project_widget.projects_dropdown.blockSignals(False)
        self.ui.project_widget.projects_dropdown.setEnabled(bool(selected_id))
        self.ui.project_widget.add_project_button.setEnabled(bool(selected_id))  # Activer/désactiver le deuxième champ


        if account_logo_url:
            pixmap = load_image_from_url(account_logo_url)
            if pixmap:
                # Redimensionner l'image
                pixmap = pixmap.scaled(self.ui.account_widget.account_image.width(), self.ui.account_widget.account_image.height(),Qt.AspectRatioMode.KeepAspectRatioByExpanding,Qt.TransformationMode.SmoothTransformation)
                self.ui.account_widget.account_image.setPixmap(pixmap)
                self.ui.account_widget.account_image.setEnabled(True)

            else:
                self.ui.account_widget.account_image.clear()
        else:
            self.ui.account_widget.account_image.clear()  # Supprime l'image si erreur
        # self.ui.account_image.setPixmap(QPixmap(account_logo_url))
        # self.ui.account_image.setEnabled(bool(account_logo_url))



    def update_projects_options_nocampaign(self):
        """Met à jour le deuxième champ en fonction de la sélection du premier."""
        selected_id, account_logo_url = self.ui.account_widget.accounts_dropdown.currentData()

        #sauvergarde des data selectionnées
        self.ui.account_id_selected = selected_id
        self.ui.account_name_selected = self.ui.account_widget.accounts_dropdown.currentText()

      


        self.ui.project_widget.projects_dropdown.blockSignals(True)
        if selected_id:
            new_options = self.model.fetch_projects(selected_id)

            # Nettoyer et mettre à jour le deuxième menu
            self.ui.project_widget.projects_dropdown.clear()
            self.ui.project_widget.project_image.clear()
            self.ui.project_widget.project_image.setEnabled(False)

            self.ui.project_widget.projects_dropdown.addItem("Selectionnez le projet..", (-1,""))
            for item_id, name, project_logo_url in new_options:
                self.ui.project_widget.projects_dropdown.addItem(name, (item_id,project_logo_url))
            self.ui.invoice_table.create_button.setEnabled(False)
            self.ui.invoice_table.modify_button.setEnabled(False)
            self.ui.invoice_table.delete_button.setEnabled(False)
        
        
        else:

            self.ui.project_widget.projects_dropdown.clear()
            self.ui.project_widget.project_image.clear()
            self.ui.project_widget.project_image.setEnabled(False)

        self.ui.project_widget.projects_dropdown.blockSignals(False)
        self.ui.project_widget.projects_dropdown.setEnabled(bool(selected_id))
        self.ui.project_widget.add_project_button.setEnabled(bool(selected_id))  # Activer/désactiver le deuxième champ


        if account_logo_url:
            pixmap = load_image_from_url(account_logo_url)
            if pixmap:
                # Redimensionner l'image
                pixmap = pixmap.scaled(self.ui.account_widget.account_image.width(), self.ui.account_widget.account_image.height(),Qt.AspectRatioMode.KeepAspectRatioByExpanding,Qt.TransformationMode.SmoothTransformation)
                self.ui.account_widget.account_image.setPixmap(pixmap)
                self.ui.account_widget.account_image.setEnabled(True)

            else:
                self.ui.account_widget.account_image.clear()
        else:
            self.ui.account_widget.account_image.clear()  # Supprime l'image si erreur
        # self.ui.account_image.setPixmap(QPixmap(account_logo_url))
        # self.ui.account_image.setEnabled(bool(account_logo_url))

    def update_projects_image(self):
        """Met à jour le deuxième champ en fonction de la sélection du premier."""
        selected_project_id, project_logo_url = self.ui.project_widget.projects_dropdown.currentData()
        if selected_project_id == -1:
            self.ui.invoice_table.create_button.setEnabled(False)
            self.ui.invoice_table.modify_button.setEnabled(False)
            self.ui.invoice_table.delete_button.setEnabled(False)

            return
        
        self.ui.invoice_table.create_button.setEnabled(True)
        self.ui.invoice_table.modify_button.setEnabled(True)
        self.ui.invoice_table.delete_button.setEnabled(True)    



        self.ui.project_id_selected = selected_project_id
        self.ui.project_name_selected = self.ui.project_widget.projects_dropdown.currentText()
                #######PARTIE PROJET car lorsqu'on clique sur un projet cette routine s'effectue :
        index = self.ui.project_widget.projects_dropdown.findText("Selectionnez le projet..")
        if index != -1:  # Vérifie que l'élément existe avant de le supprimer
            self.ui.project_widget.projects_dropdown.blockSignals(True)
            self.ui.project_widget.projects_dropdown.removeItem(index)
            self.ui.project_widget.projects_dropdown.blockSignals(False)
        #Update image projet
        # self.ui.groupcampaigns_dropdown.setEnabled(bool(selected_project_id))  # Activer/désactiver le deuxième champ
        if project_logo_url:
            pixmap = load_image_from_url(project_logo_url)
            if pixmap:
                # Redimensionner l'image
                pixmap = pixmap.scaled(self.ui.project_widget.project_image.width(), self.ui.project_widget.project_image.height(),Qt.AspectRatioMode.KeepAspectRatioByExpanding,Qt.TransformationMode.SmoothTransformation)
                self.ui.project_widget.project_image.setPixmap(pixmap)
                self.ui.project_widget.project_image.setEnabled(True)

            else:
                self.ui.project_widget.project_image.clear()
        else:
            self.ui.project_widget.project_image.clear()  # Supprime l'image si erreur

        ######FIN de la partie PROJET

    def update_invoices(self):
        selected_project_id, project_logo_url = self.ui.project_widget.projects_dropdown.currentData()
        if selected_project_id == -1:

            return
        
        invoice_data = self.model.fetch_invoice(selected_project_id)
        # Vider le tableau avant d'ajouter les nouvelles données
        self.ui.invoice_table.model.removeRows(0, self.ui.invoice_table.model.rowCount())

        for row, values in enumerate(invoice_data):
            for col, value in enumerate(values):
                item = QStandardItem(str(value))
                # self.ui.model.setItem(row, col, item))
                if col in self.ui.invoice_table.locked_columns:
                    item.setFlags(item.flags() & ~Qt.ItemFlag.ItemIsEditable)
                self.ui.invoice_table.model.setItem(row, col, item)
                # Colorer la cellule "state" en fonction de sa valeur
                if col == 5:  # Colonne "state"
                    state_value = str(value)
                    if state_value == "Paid":
                        item.setBackground(QColor("#4CAF50"))  # Vert
                        item.setForeground(QColor("white"))   # Texte blanc pour meilleure lisibilité
                    elif state_value == "Waiting":
                        item.setBackground(QColor("#FF9800"))  # Orange
                        item.setForeground(QColor("white"))   # Texte blanc




    def update_groupcampaigns_options(self):
        """Met à jour le deuxième champ en fonction de la sélection du premier."""
        selected_account_id, account_logo_url = self.ui.account_widget.accounts_dropdown.currentData()
        # print("lancement de l'update des groupcampaigns")
        self.ui.group_campaign_table.model.removeRows(0, self.ui.group_campaign_table.model.rowCount())

        if not self.ui.project_widget.projects_dropdown.currentData() :
            # self.ui.campaigns_table.create_button.setEnabled(False)
            # self.ui.campaigns_table.modify_button.setEnabled(False)
            # self.ui.campaigns_table.delete_button.setEnabled(False)
            return
        selected_project_id, project_logo_url = self.ui.project_widget.projects_dropdown.currentData()
        if selected_project_id == -1:
            # self.ui.campaigns_table.create_button.setEnabled(False)
            # self.ui.campaigns_table.modify_button.setEnabled(False)
            # self.ui.campaigns_table.delete_button.setEnabled(False)

            return
        self.ui.project_id_selected = selected_project_id
        self.ui.project_name_selected = self.ui.project_widget.projects_dropdown.currentText()
        # print("Projet id : ",self.ui.project_id_selected)
        # print("Projet name : ",self.ui.project_name_selected)
        self.ui.group_campaign_table.add_group_button.setEnabled(True)
        self.ui.group_campaign_table.delete_group_button.setEnabled(True)
        self.ui.group_campaign_table.modify_group_button.setEnabled(True)




        #######PARTIE PROJET car lorsqu'on clique sur un projet cette routine s'effectue :
        index = self.ui.project_widget.projects_dropdown.findText("Selectionnez le projet..")
        if index != -1:  # Vérifie que l'élément existe avant de le supprimer
            self.ui.project_widget.projects_dropdown.blockSignals(True)
            self.ui.project_widget.projects_dropdown.removeItem(index)
            self.ui.project_widget.projects_dropdown.blockSignals(False)
        #Update image projet
        # self.ui.groupcampaigns_dropdown.setEnabled(bool(selected_project_id))  # Activer/désactiver le deuxième champ
        if project_logo_url:
            pixmap = load_image_from_url(project_logo_url)
            if pixmap:
                # Redimensionner l'image
                pixmap = pixmap.scaled(self.ui.project_widget.project_image.width(), self.ui.project_widget.project_image.height(),Qt.AspectRatioMode.KeepAspectRatioByExpanding,Qt.TransformationMode.SmoothTransformation)
                self.ui.project_widget.project_image.setPixmap(pixmap)
                self.ui.project_widget.project_image.setEnabled(True)

            else:
                self.ui.project_widget.project_image.clear()
        else:
            self.ui.project_widget.project_image.clear()  # Supprime l'image si erreur

        ######FIN de la partie PROJET

        if selected_project_id:
            groupscampaingsdata = self.model.fetch_groupcampaigns(selected_project_id,selected_account_id)


        for row, values in enumerate(groupscampaingsdata):
            for col, value in enumerate(values):
                item = QStandardItem(str(value))
                # self.ui.model.setItem(row, col, item))
                if col in self.ui.group_campaign_table.locked_columns:
                    item.setFlags(item.flags() & ~Qt.ItemFlag.ItemIsEditable)
                self.ui.group_campaign_table.model.setItem(row, col, item)

        #     # Nettoyer et mettre à jour le deuxième menu
        #     self.ui.groupcampaigns_dropdown.clear()
        #     for item_id, name in new_options:
        #         self.ui.groupcampaigns_dropdown.addItem(name, item_id)
        # else:
        #     self.ui.groupcampaigns_dropdown.clear()



        # self.ui.account_image.setPixmap(QPixmap(account_logo_url))
        # self.ui.account_image.setEnabled(bool(account_logo_url))


    def update_campaigns_options(self):
        """Met à jour le deuxième champ en fonction de la sélection du premier."""
        selected_account_id, account_logo_url = self.ui.account_widget.accounts_dropdown.currentData()
        # print("lancement de l'update des campaigns")
        self.ui.campaigns_table.model.removeRows(0, self.ui.campaigns_table.model.rowCount())

        if not self.ui.project_widget.projects_dropdown.currentData() :
            return
        selected_project_id, project_logo_url = self.ui.project_widget.projects_dropdown.currentData()
        if selected_project_id == -1:
            return

        selection_model = self.ui.group_campaign_table.table.selectionModel()
        if not selection_model or not selection_model.hasSelection():
            return

        selected_indexes = selection_model.selectedRows()
        if not selected_indexes:
            self.ui.campaigns_table.create_button.setEnabled(False)
            self.ui.campaigns_table.modify_button.setEnabled(False)
            self.ui.campaigns_table.delete_button.setEnabled(False)
            self.ui.campaigns_table.view_button.setEnabled(False)
            return

        self.ui.campaigns_table.create_button.setEnabled(True)
        self.ui.campaigns_table.modify_button.setEnabled(True)
        self.ui.campaigns_table.delete_button.setEnabled(True)
        self.ui.campaigns_table.view_button.setEnabled(True)

        row = selected_indexes[0].row()

        # 5. Récupérer l’élément dans la colonne 0 (l’ID)
        model = self.ui.group_campaign_table.model
        id_index = model.index(row, 0)  # Ligne sélectionnée, colonne 0
        selected_groupcampaign_id = int(model.data(id_index))
        self.ui.group_id_selected = selected_groupcampaign_id
        name_index = model.index(row, 2)  # Ligne sélectionnée, colonne 1 (name)
        self.ui.group_name_selected = model.data(name_index)

        print("account_id :", selected_account_id)
        print("project_id :", selected_project_id)
        print("groupcampaign_id :", selected_groupcampaign_id)



        # Récupérer les données des comptes admin et ad
        admin_accounts_data, ad_accounts_data = self.model.fetch_bmaccounts()

        # Fonction pour trouver le nom d'un compte à partir de son ID
        def get_account_name(account_id, accounts_data):
            for account in accounts_data:
                if account.get("id_account") == account_id:
                    return account.get("name", "N/A")
            return account_id  # Retourne l'ID si le nom n'est pas trouvé

        if selected_groupcampaign_id:
            campaingsdata = self.model.fetch_campaigns(selected_project_id,selected_account_id,selected_groupcampaign_id)

        # Trier les campagnes : actives en premier, puis waiting, puis les autres
        # Créer des listes séparées pour chaque état
        active_campaigns = []
        waiting_campaigns = []
        other_campaigns = []

        for campaign in campaingsdata:
            # L'état est à l'index 6
            state = str(campaign[6]).lower()
            if state == "active":
                active_campaigns.append(campaign)
            elif state == "waiting":
                waiting_campaigns.append(campaign)
            else:
                other_campaigns.append(campaign)

        # Combiner les listes dans l'ordre souhaité
        sorted_campaigns = active_campaigns + waiting_campaigns + other_campaigns

        print("campaign data (triées) : ", sorted_campaigns)
        for row, values in enumerate(sorted_campaigns):
            for col, value in enumerate(values):
                # Convertir les dates (colonnes 7 et 8 sont start_date et end_date)
                if col == 7 or col == 8:  # Indices des colonnes de dates
                    value = convert_date_format(str(value))
                # Remplacer les IDs des comptes admin par leurs noms (colonne 9)
                elif col == 11:  # ID_adminadaccount
                    value = get_account_name(value, admin_accounts_data)
                # Remplacer les IDs des comptes ad par leurs noms (colonne 10)
                elif col == 12:  # ID_AdAccount
                    value = get_account_name(value, ad_accounts_data)

                item = QStandardItem(str(value))

                # Colorer la cellule State (colonne 6) en fonction de sa valeur
                if col == 6:  # Colonne State
                    state_value = str(value).lower()
                    if state_value == "active":
                        item.setBackground(QColor("#4CAF50"))  # Vert
                        item.setForeground(QColor("white"))   # Texte blanc pour meilleure lisibilité
                    elif state_value == "waiting":
                        item.setBackground(QColor("#FF9800"))  # Orange
                        item.setForeground(QColor("white"))   # Texte blanc
                    elif state_value == "rejected":
                        item.setBackground(QColor("#F44336"))  # Rouge
                        item.setForeground(QColor("white"))   # Texte blanc
                    elif state_value == "paused":
                        item.setBackground(QColor("#2196F3"))  # Bleu
                        item.setForeground(QColor("white"))   # Texte blanc
                    elif state_value == "ended":
                        item.setBackground(QColor("#9E9E9E"))  # Gris
                        item.setForeground(QColor("white"))   # Texte blanc

                # self.ui.model.setItem(row, col, item))
                if col in self.ui.campaigns_table.locked_columns:
                    item.setFlags(item.flags() & ~Qt.ItemFlag.ItemIsEditable)
                self.ui.campaigns_table.model.setItem(row, col, item)

        # Mettre à jour les filtres après avoir chargé les données
        # self.ui.campaignsfilter.update_filters_from_table(self.ui.campaigns_table.model)

        #     # Nettoyer et mettre à jour le deuxième menu
        #     self.ui.groupcampaigns_dropdown.clear()
        #     for item_id, name in new_options:
        #         self.ui.groupcampaigns_dropdown.addItem(name, item_id)
        # else:
        #     self.ui.groupcampaigns_dropdown.clear()



        # self.ui.account_image.setPixmap(QPixmap(account_logo_url))
        # self.ui.account_image.setEnabled(bool(account_logo_url))

    def update_campaign_dialog(self,campaing_id):

        return self.model.fetch_campaign_id(campaing_id)

    def update_bmaccounts(self):

        return self.model.fetch_bmaccounts()




    def update_all_campaigns_options(self):
        """Met à jour le tableau des campagnes avec toutes les campagnes, en triant les campagnes actives en premier."""
        # Récupérer les données des comptes admin et ad
        admin_accounts_data, ad_accounts_data = self.model.fetch_bmaccounts()

        # Fonction pour trouver le nom d'un compte à partir de son ID
        def get_account_name(account_id, accounts_data):
            for account in accounts_data:
                if account.get("id_account") == account_id:
                    return account.get("name", "N/A")
            return account_id  # Retourne l'ID si le nom n'est pas trouvé

        # Récupérer toutes les campagnes
        campaingsdata,campaign_columns_index = self.model.fetch_allcampaigns()

        # Stocker l'index des colonnes dans l'interface
        self.ui.campaigns_table.campaign_columns_index = campaign_columns_index
        # Trier les campagnes : actives en premier, puis waiting, puis les autres
        # Créer des listes séparées pour chaque état
        active_campaigns = []
        waiting_campaigns = []
        other_campaigns = []
        
        state_index = campaign_columns_index['state']
        start_date_index = campaign_columns_index['start_date']
        end_date_index = campaign_columns_index['end_date']
        admin_account_index = campaign_columns_index['ID_adminadaccount']
        ad_account_index = campaign_columns_index['ID_AdAccount']

        for campaign in campaingsdata:
            state = str(campaign[state_index]).lower()
            if state == "active":
                active_campaigns.append(campaign)
            elif state == "waiting":
                waiting_campaigns.append(campaign)
            else:
                other_campaigns.append(campaign)

        # Combiner les listes dans l'ordre souhaité
        sorted_campaigns = active_campaigns + waiting_campaigns + other_campaigns

        # Vider le modèle avant d'ajouter les nouvelles données
        self.ui.campaigns_table.model.removeRows(0, self.ui.campaigns_table.model.rowCount())

        # print("campaign data (triées) : ", sorted_campaigns)
        for row, values in enumerate(sorted_campaigns):
            for col, value in enumerate(values):
                # Convertir les dates
                if col in [start_date_index, end_date_index]:
                    value = convert_date_format(str(value))
                # Remplacer les IDs des comptes admin par leurs noms
                elif col == admin_account_index:
                    value = get_account_name(value, admin_accounts_data)
                # Remplacer les IDs des comptes ad par leurs noms
                elif col == ad_account_index:
                    value = get_account_name(value, ad_accounts_data)

                item = QStandardItem(str(value))


                # self.ui.model.setItem(row, col, item))
                if col in self.ui.campaigns_table.locked_columns:
                    item.setFlags(item.flags() & ~Qt.ItemFlag.ItemIsEditable)
                self.ui.campaigns_table.model.setItem(row, col, item)

        # Mettre à jour les filtres après avoir chargé les données
        self.ui.campaignsfilter.update_filters_from_table(self.ui.campaigns_table.model)

        #     # Nettoyer et mettre à jour le deuxième menu
        #     self.ui.groupcampaigns_dropdown.clear()
        #     for item_id, name in new_options:
        #         self.ui.groupcampaigns_dropdown.addItem(name, item_id)
        # else:
        #     self.ui.groupcampaigns_dropdown.clear()



        # self.ui.account_image.setPixmap(QPixmap(account_logo_url))
        # self.ui.account_image.setEnabled(bool(account_logo_url))



    def validate_project_name(self, project_name):
        """Vérifie si le nom du projet est valide."""
        if not project_name.strip():
            return False, "Le champ ne peut pas être vide."

        if len(project_name) > 32:
            return False, "Nom trop long, maximum 32 caractères."

        if not re.match(r'^[a-zA-Z0-9 ]+$', project_name):
            return False, "Caractères spéciaux non autorisés."

        return True, None

    def validate_groupcampaign_name(self, project_name):
        """Vérifie si le nom du projet est valide."""
        if not project_name.strip():
            return False, "Le champ ne peut pas être vide."

        if len(project_name) > 50:
            return False, "Nom trop long, maximum 50 caractères."

        # if not re.match(r'^[a-zA-Z0-9 àéèêëîïôöûüùÀÉÈÊËÎÏÔÖÛÜÙ&\'^¨/\-\'(),\.:]+$', project_name):
        #     return False, "Caractères non autorisés. Seuls les lettres, chiffres, espaces, accents, et les caractères ( ) - ' . , & ^ ¨ / : sont permis."

        return True, None


    def send_account_to_xano(self, account_name, logo_path):

        return self.model.post_account(account_name, logo_path)




    def send_project_to_xano(self,account_id, project_name, logo_path):

        return self.model.post_project(account_id, project_name, logo_path)


    def send_campaign_to_xano(self, data):

        return self.model.post_campaign(data)

    def modify_campaign_to_xano(self, data):

        return self.model.modify_campaign(data)


    def send_groupcampaign_to_xano(self, project_id, groupcampaign_name):

        return self.model.post_groupcampaign(project_id, groupcampaign_name)

    def modify_groupcampaign_to_xano(self, project_id, groupcampaign_name,groupcampaign_id):

        return self.model.modify_groupcampaign(project_id, groupcampaign_name,groupcampaign_id)


    def del_groupcampaign_to_xano(self, project_id, groupcampaign_name):

        return self.model.delete_groupcampaign(project_id, groupcampaign_name)

    


    



    def send_form_data_to_db(self, client, option):
        """Envoie les données du formulaire à la base de données."""
        success = self.model.send_form_data(client, option)
        if success:
            self.ui.show_message("Succès", "Les données ont été envoyées avec succès.")
        else:
            self.ui.show_message("Erreur", "Échec de l'envoi des données.")


##### INVOICE ####

    def send_invoice_file_to_S3(self, file_path, invoice_name,invoice_num):

        print(file_path)
        print(invoice_name)
        print(invoice_num)
        filename = self.generate_unique_filename(invoice_name, invoice_num)
        print(filename)

        self.model.upload_invoice_to_s3(file_path,filename)

        return filename
    
    def generate_unique_filename(self, invoice_name: str, invoice_number: str, extension="pdf") -> str:
        # Nettoyer les champs pour éviter les caractères interdits dans les noms de fichier
        safe_name = re.sub(r'[^a-zA-Z0-9_-]', '_', invoice_name.strip())
        safe_number = re.sub(r'[^a-zA-Z0-9_-]', '_', invoice_number.strip())

        # Timestamp au format YYYYMMDD_HHMMSS
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Concaténer tous les éléments
        filename = f"SAcorp_{safe_name}_{safe_number}_{timestamp}.{extension}"
        return filename
    
    def send_invoice_to_xano(self, project_id, name, number, price, state,date, file_name):

        return self.model.post_invoice(project_id, name, number, price, state,date, file_name)


    def del_invoice_file_from_S3(self, invoice_name):

        return self.model.delete_invoice_from_s3(invoice_name)

    def del_invoice_from_xano(self, invoice_id):

        return self.model.delete_invoice(invoice_id)


    def modify_invoice_to_xano(self, data):

        return self.model.modify_invoice(data)



##### UTILITAIRES #####

def load_image_from_url(url):

    """Télécharge une image depuis une URL et retourne un QPixmap."""
    try:
        response = requests.get(url, headers={"User-Agent": "Mozilla/5.0"})  # ✅ Ajoute un User-Agent pour éviter le blocage
        response.raise_for_status()  # ✅ Vérifie si la requête est OK (évite les erreurs 403/404)

        image_data = QByteArray(response.content)  # ✅ Convertir les données en binaire
        pixmap = QPixmap()
        if not pixmap.loadFromData(image_data):  # ✅ Vérifier si PyQt arrive à charger l'image
            print("Erreur : Impossible de charger l’image avec QPixmap")
            return None

        return pixmap

    except requests.exceptions.RequestException as e:

        print(f"Erreur lors du téléchargement de l'image : {e}")
        return None




