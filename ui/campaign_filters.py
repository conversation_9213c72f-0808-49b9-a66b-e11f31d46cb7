"""
Widget pour les filtres de campagnes.
"""

from PyQt6.QtWidgets import <PERSON>Widget, QHBoxLayout, QLineEdit, QComboBox, QPushButton, QDateEdit
from PyQt6.QtCore import Qt, QDate

class CampaignFiltersWidget(QWidget):
    def __init__(self, campaigns_table_widget):
        super().__init__()
        self.campaigns_table_widget = campaigns_table_widget
        self.layout = QHBoxLayout()
        self.layout.setSpacing(10)

        # Champ de recherche
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Rechercher une campagne")
        self.layout.addWidget(self.search_input)

        # Filtre par levier
        self.levier_filter = QComboBox()
        self.levier_filter.addItem("Levier")
        self.layout.addWidget(self.levier_filter)

        # Filtre par objectif
        self.objectif_filter = QComboBox()
        self.objectif_filter.addItem("Objectif")
        self.layout.addWidget(self.objectif_filter)

        # Filtre par statut
        self.status_filter = QComboBox()
        self.status_filter.addItem("Statut")
        self.layout.addWidget(self.status_filter)

        # Filtre par date de début
        self.start_date_filter = QDateEdit()
        self.start_date_filter.setCalendarPopup(True)
        self.start_date_filter.setDate(QDate.currentDate())
        self.start_date_filter.setDisplayFormat("yyyy-MM-dd")
        self.start_date_filter.setStyleSheet("""
            QDateEdit {
                border: 1px solid gray;
                border-radius: 3px;
                padding: 1px 18px 1px 3px;
                min-width: 6em;
            }
            QDateEdit::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 15px;
                border-left-width: 1px;
                border-left-color: darkgray;
                border-left-style: solid;
                border-top-right-radius: 3px;
                border-bottom-right-radius: 3px;
            }
        """)
        self.start_date_filter.lineEdit().setReadOnly(True)
        self.start_date_filter.lineEdit().setCursor(Qt.CursorShape.ArrowCursor)
        self.layout.addWidget(self.start_date_filter)

        # Bouton de réinitialisation
        self.reset_button = QPushButton("Réinitialiser")
        self.reset_button.clicked.connect(self.reset_filters)
        self.layout.addWidget(self.reset_button)
        
        self.setLayout(self.layout)

        # Connecter les filtres aux événements
        self.search_input.textChanged.connect(self.filter_table)
        self.start_date_filter.dateChanged.connect(self.filter_table)
        self.levier_filter.currentIndexChanged.connect(self.filter_table)
        self.objectif_filter.currentIndexChanged.connect(self.filter_table)
        self.status_filter.currentIndexChanged.connect(self.filter_table)

    def update_filters_from_table(self, model):
        """Met à jour les options des filtres en fonction des données du tableau."""
        # Réinitialiser les filtres
        self.levier_filter.clear()
        self.objectif_filter.clear()
        self.status_filter.clear()
        
        # Ajouter les options par défaut
        self.levier_filter.addItem("Levier")
        self.objectif_filter.addItem("Objectif")
        self.status_filter.addItem("Statut")
        
        # Ensembles pour stocker les valeurs uniques
        leviers = set()
        objectifs = set()
        statuses = set()
        
        # Parcourir le modèle pour extraire les valeurs uniques
        for row in range(model.rowCount()):
            levier = model.item(row, 2).text() if model.item(row, 2) else ""
            objectif = model.item(row, 3).text() if model.item(row, 3) else ""
            status = model.item(row, 6).text() if model.item(row, 6) else ""
            
            if levier:
                leviers.add(levier)
            if objectif:
                objectifs.add(objectif)
            if status:
                statuses.add(status)
        
        # Ajouter les valeurs uniques aux filtres
        for levier in sorted(leviers):
            self.levier_filter.addItem(levier)
        for objectif in sorted(objectifs):
            self.objectif_filter.addItem(objectif)
        for status in sorted(statuses):
            self.status_filter.addItem(status)

    def filter_table(self):
        """Filtre le tableau en fonction des critères sélectionnés."""
        search_text = self.search_input.text().lower()
        levier_filter = self.levier_filter.currentText()
        objectif_filter = self.objectif_filter.currentText()
        status_filter = self.status_filter.currentText()
        start_date = self.start_date_filter.date().toString("yyyy-MM-dd")
        
        # Ne pas filtrer si l'option par défaut est sélectionnée
        if levier_filter == "Levier":
            levier_filter = ""
        if objectif_filter == "Objectif":
            objectif_filter = ""
        if status_filter == "Statut":
            status_filter = ""
        
        # Parcourir toutes les lignes du tableau
        for row in range(self.campaigns_table_widget.model.rowCount()):
            show_row = True
            
            # Vérifier le texte de recherche
            if search_text:
                row_text = ""
                for col in range(self.campaigns_table_widget.model.columnCount()):
                    item = self.campaigns_table_widget.model.item(row, col)
                    if item:
                        row_text += item.text().lower() + " "
                if search_text not in row_text:
                    show_row = False
            
            # Vérifier le filtre de levier
            if levier_filter and self.campaigns_table_widget.model.item(row, 2) and self.campaigns_table_widget.model.item(row, 2).text() != levier_filter:
                show_row = False
            
            # Vérifier le filtre d'objectif
            if objectif_filter and self.campaigns_table_widget.model.item(row, 3) and self.campaigns_table_widget.model.item(row, 3).text() != objectif_filter:
                show_row = False
            
            # Vérifier le filtre de statut
            if status_filter and self.campaigns_table_widget.model.item(row, 6) and self.campaigns_table_widget.model.item(row, 6).text() != status_filter:
                show_row = False
            
            # Vérifier la date de début
            if start_date and self.campaigns_table_widget.model.item(row, 7) and self.campaigns_table_widget.model.item(row, 7).text() < start_date:
                show_row = False
            
            # Afficher ou masquer la ligne
            self.campaigns_table_widget.table.setRowHidden(row, not show_row)

    def reset_filters(self):
        """Réinitialise tous les filtres."""
        self.search_input.clear()
        self.levier_filter.setCurrentIndex(0)
        self.objectif_filter.setCurrentIndex(0)
        self.status_filter.setCurrentIndex(0)
        self.start_date_filter.setDate(QDate.currentDate())
        
        # Afficher toutes les lignes
        for row in range(self.campaigns_table_widget.model.rowCount()):
            self.campaigns_table_widget.table.setRowHidden(row, False)
