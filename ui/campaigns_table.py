"""
Widget pour la gestion des campagnes.
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
                            QTableView, QMessageBox, QHeaderView)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QStandardItemModel, QStandardItem, QPixmap
import os
import sys

from .campaign_dialog import AddCampaignDialog

class CampaignsTableWidget(QWidget):
    def __init__(self, font_manager, controller):
        super().__init__()

        self.font_manager = font_manager
        self.controller = controller
        
        # Création du modèle avec colonnes
        self.model = QStandardItemModel(0, 12)  # 0 lignes, 12 colonnes
        self.model.setHorizontalHeaderLabels([
            "ID", "Nom de la campagne", "levier", "Objectif", 
            "Budget margé", "Budget non margé", "State", "Start Date", 
            "End Date", "ID Admin account", "ID Ad Account", "ID Campaign"
        ])

        self.locked_columns = [0]  # Colonnes verrouillées (ID)
        self.modified_rows = set()  # Stocke les lignes modifiées

        # Création du tableau
        self.table = QTableView()
        self.table.setSortingEnabled(True)  # Active le tri au clic
        self.table.setModel(self.model)

        # Activer la sélection de toute la ligne
        self.table.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.table.setSelectionMode(QTableView.SelectionMode.SingleSelection)
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.table.setStyleSheet("""
            QTableView::item:selected {
                background-color: #476AFF;
                color: white;
            }
        """)

        # Connecter le signal pour détecter les modifications
        self.model.dataChanged.connect(self.highlight_modified_row)

        # Layout principal
        mainlayout = QVBoxLayout()

        # Boutons pour les actions sur les campagnes
        button_layout = QHBoxLayout()
        button_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)
        
        self.create_button = QPushButton("Creer")
        self.create_button.setMaximumWidth(100)
        self.create_button.setEnabled(False)
        
        self.modify_button = QPushButton("Modifier")
        self.modify_button.setMaximumWidth(100)
        self.modify_button.setEnabled(False)
        
        self.delete_button = QPushButton("Supprimer")
        self.delete_button.setMaximumWidth(100)
        self.delete_button.setEnabled(False)
        
        self.view_button = QPushButton("Voir")
        self.view_button.setMaximumWidth(100)
        self.view_button.setEnabled(False)
        
        button_layout.addWidget(self.create_button)
        button_layout.addWidget(self.modify_button)
        button_layout.addWidget(self.delete_button)
        button_layout.addWidget(self.view_button)
        
        mainlayout.addLayout(button_layout)
        mainlayout.addWidget(self.table)
        
        self.setLayout(mainlayout)

    def highlight_modified_row(self, top_left, bottom_right):
        """Met en évidence les lignes modifiées."""
        for row in range(top_left.row(), bottom_right.row() + 1):
            self.modified_rows.add(row)
            
            # Modifier la couleur si la ligne est sélectionnée
            self.table.setStyleSheet(f"""
                QTableView::item:selected {{
                    background-color: #FF8C00;  /* Orange foncé uniquement pour les lignes modifiées */
                    color: white;
                }}
            """)

    def update_data(self, data):
        """Met à jour les données du tableau en gardant les colonnes verrouillées"""
        self.model.removeRows(0, self.model.rowCount())  # Supprime les anciennes lignes

        for row, values in enumerate(data):
            for col, value in enumerate(values):
                item = QStandardItem(str(value))
                if isinstance(value, int):  # Permet un tri correct des nombres
                    item.setData(value, Qt.ItemDataRole.EditRole)

                # Désactiver l'édition pour les colonnes verrouillées
                if col in self.locked_columns:
                    item.setFlags(item.flags() & ~Qt.ItemFlag.ItemIsEditable)

                self.model.setItem(row, col, item)

    def get_table(self):
        """Retourne le QTableView pour être ajouté ailleurs"""
        return self.table

    def open_add_campaign_window(self, account_id_selected, account_name_selected, project_id_selected, project_name_selected, group_id_selected, group_name_selected, controller):
        """Ouvre une nouvelle fenêtre pour ajouter une campagne"""
        dialog = AddCampaignDialog(account_id_selected, account_name_selected, project_id_selected, project_name_selected, group_id_selected, group_name_selected, controller, self.font_manager)
        dialog.exec()  # Affiche la boîte de dialogue en mode bloquant

    def open_modify_campaign_window(self, account_id_selected, account_name_selected, project_id_selected, project_name_selected, group_id_selected, group_name_selected, controller):
        """Ouvre une nouvelle fenêtre pour modifier une campagne"""
        selection_model = self.table.selectionModel()
        selected_rows = selection_model.selectedRows()
        if not selected_rows:
            QMessageBox.warning(self, "Erreur", "Veuillez sélectionner une campagne.")
            return

        selected_index = selected_rows[0]
        campaign_id = int(self.model.item(selected_index.row(), 0).text())
        
        dialog = AddCampaignDialog(account_id_selected, account_name_selected, project_id_selected, project_name_selected, group_id_selected, group_name_selected, controller, self.font_manager, campaign_id=campaign_id)
        dialog.exec()  # Affiche la boîte de dialogue en mode bloquant

    def open_view_campaign_window(self, account_id_selected, account_name_selected, project_id_selected, project_name_selected, group_id_selected, group_name_selected, controller):
        """Ouvre une nouvelle fenêtre pour visualiser une campagne en lecture seule"""
        selection_model = self.table.selectionModel()
        selected_rows = selection_model.selectedRows()
        if not selected_rows:
            QMessageBox.warning(self, "Erreur", "Veuillez sélectionner une campagne.")
            return

        selected_index = selected_rows[0]
        campaign_id = int(self.model.item(selected_index.row(), 0).text())
        
        dialog = AddCampaignDialog(account_id_selected, account_name_selected, project_id_selected, project_name_selected, group_id_selected, group_name_selected, controller, self.font_manager, campaign_id=campaign_id, read_only=True)
        dialog.exec()  # Affiche la boîte de dialogue en mode bloquant
        
    def del_campaign(self, group_id_selected):
        """Supprime une campagne"""
        selection_model = self.table.selectionModel()
        selected_rows = selection_model.selectedRows()
        if not selected_rows:
            msg_box = QMessageBox()
            msg_box.setWindowTitle("Erreur")
            msg_box.setText("Veuillez sélectionner une campagne.")
            
            # Définir une icône personnalisée
            if getattr(sys, 'frozen', False):
                base_path = sys._MEIPASS
                assets_path = os.path.join(base_path, "assets", "icons")
            else:
                assets_path = os.path.abspath("assets/icons/")
                
            icon_path = os.path.join(assets_path, "custom_error.png")
            if os.path.exists(icon_path):
                msg_box.setIconPixmap(QPixmap(icon_path).scaled(48, 48, Qt.AspectRatioMode.KeepAspectRatio))
            msg_box.exec()
            return

        selected_index = selected_rows[0]
        campaign_id = int(self.model.item(selected_index.row(), 0).text())

        # Confirmation de suppression
        confirmation = QMessageBox.question(
            self,
            "Confirmation de suppression",
            "Êtes-vous sûr de vouloir supprimer cette campagne ?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if confirmation != QMessageBox.StandardButton.Yes:
            return

        success, message = self.controller.del_campaign_to_xano(group_id_selected, campaign_id)
        
        if success:
            msg_box = QMessageBox()
            msg_box.setWindowTitle("Succès")
            msg_box.setText("Campagne supprimée avec succès.")
            
            # Définir une icône personnalisée
            if getattr(sys, 'frozen', False):
                base_path = sys._MEIPASS
                assets_path = os.path.join(base_path, "assets", "icons")
            else:
                assets_path = os.path.abspath("assets/icons/")
                
            icon_path = os.path.join(assets_path, "custom_success.png")
            if os.path.exists(icon_path):
                msg_box.setIconPixmap(QPixmap(icon_path).scaled(48, 48, Qt.AspectRatioMode.KeepAspectRatio))
            msg_box.exec()
        else:
            QMessageBox.warning(self, "Erreur", f"Échec de la suppression de la campagne : {message}")
            
        self.controller.update_campaigns_options()
