"""
Fenêtre principale de l'application.
"""

from PyQt6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QMessageBox, QMainWindow
from PyQt6.QtCore import Qt

from ui_accountcampaign.account_widget import AccountWidget
from ui_accountcampaign.project_widget import ProjectWidget
from .invoice_table import InvoiceTableWidget



class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        from controller import DataController  # Import ici pour éviter les imports circulaires
        self.controller = DataController(self)  # Contrôleur pour gérer les requêtes
        from tools.font_manager import FontManager
        self.font_manager = FontManager()
        self.init_ui()

    def init_ui(self):
        self.setWindowTitle("SA Corp - Saas Backoffice")
        self.setGeometry(100, 100, 1500, 700)
        self.setFont(self.font_manager.get_font("regular", 12))

        # Créer un widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Créer la barre de menu
        self.create_menu_bar()

        # Layout principal pour le widget central
        layout = QVBoxLayout(central_widget)

        # Layout de navigation supérieure
        topnav_layout = QHBoxLayout()
        topnav_layout.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignTop)
        topnav_left_layout = QHBoxLayout()
        topnav_left_layout.setAlignment(Qt.AlignmentFlag.AlignTop)  # S'assurer que tout est aligné en haut

        # Widgets pour les comptes et projets
        self.account_widget = AccountWidget(self.font_manager)
        topnav_left_layout.addWidget(self.account_widget.get_layout())
        self.project_widget = ProjectWidget(self.font_manager)
        topnav_left_layout.addWidget(self.project_widget.get_layout())
        topnav_left_layout.addSpacing(20)  # Ajoute un peu d'espace entre les éléments
        topnav_layout.addLayout(topnav_left_layout)

        # Tableau des Invoices

        self.invoice_table = InvoiceTableWidget(self.font_manager, self.controller)
        topnav_layout.addWidget(self.invoice_table)


        layout.addLayout(topnav_layout)

        # Tableau des campagnes et filtres

        # Charger les premiers choix
        self.controller.load_initial_options()

        # Connecter le premier dropdown au changement
        self.account_widget.accounts_dropdown.currentIndexChanged.connect(
            lambda _: self.controller.update_projects_options_nocampaign() if self.account_widget.accounts_dropdown.count() > 0 else None
        )

        # Reset invoice table when account changes
        self.account_widget.accounts_dropdown.currentIndexChanged.connect(
            lambda: self.invoice_table.model.removeRows(0, self.invoice_table.model.rowCount())
        )
        self.project_widget.projects_dropdown.currentIndexChanged.connect(self.controller.update_invoices)
        



        #Account
        self.account_widget.add_account_button.clicked.connect(
            lambda: self.account_widget.open_add_account_window(self.controller)
        )
        self.account_widget.user_account_button.clicked.connect(
            lambda: self.account_widget.open_user_account_window(self.account_id_selected,self.account_name_selected,self.controller)
        )
        

        #project

        self.project_widget.add_project_button.clicked.connect(
            lambda: self.project_widget.open_add_project_window(self.account_id_selected, self.account_name_selected,self.controller)
        )

        self.project_widget.projects_dropdown.currentIndexChanged.connect(self.controller.update_projects_image)
        self.project_widget.projects_dropdown.currentIndexChanged.connect(self.controller.update_invoices)

        #Invoice
        self.invoice_table.create_button.clicked.connect(
            lambda: self.invoice_table.open_add_invoice_window(self.account_id_selected, self.account_name_selected,self.project_id_selected, self.project_name_selected,self.controller)
        )
        self.invoice_table.delete_button.clicked.connect(
            lambda: self.invoice_table.del_invoice(self.project_id_selected)
        )
        self.invoice_table.modify_button.clicked.connect(
            lambda: self.invoice_table.open_modify_invoice_window(self.account_id_selected, self.account_name_selected,self.project_id_selected, self.project_name_selected,self.controller)
        )









    def closeEvent(self, event):
        print("App fermée proprement")
        # Arrêter les timers, threads, sauvegarder l’état, etc.
        event.accept()

    def show_message(self, title, message):
        """Affiche un message à l'utilisateur."""
        QMessageBox.information(self, title, message)

    def create_menu_bar(self):
        """Crée la barre de menu de l'application."""
        menu_bar = self.menuBar()

        # Menu Fichier
        file_menu = menu_bar.addMenu("Fichier")

        # Action Retour au menu principal
        main_menu_action = file_menu.addAction("Retour au menu principal")
        main_menu_action.triggered.connect(self.return_to_main_menu)

        # Action Réinitialiser
        reset_action = file_menu.addAction("Réinitialiser l'application")
        reset_action.triggered.connect(self.reset_application)

        # Ajouter un séparateur
        file_menu.addSeparator()

        # Action Quitter
        quit_action = file_menu.addAction("Quitter")
        quit_action.triggered.connect(self.close)

    def reset_application(self):
        """Réinitialise l'application en rechargeant les données depuis la base de données."""
        # Demander confirmation à l'utilisateur
        reply = QMessageBox.question(self, "Confirmation",
                                   "Voulez-vous vraiment réinitialiser l'application ? \n\nCela rechargera toutes les données depuis la base de données.",
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)

        if reply == QMessageBox.StandardButton.Yes:
            # Vider les tableaux
            self.group_campaign_table.model.removeRows(0, self.group_campaign_table.model.rowCount())
            self.campaigns_table.model.removeRows(0, self.campaigns_table.model.rowCount())

            # Réinitialiser les dropdowns
            self.account_widget.accounts_dropdown.clear()
            self.project_widget.projects_dropdown.clear()

            # Réinitialiser les filtres
            self.campaignsfilter.reset_filters()

            # Recharger les données initiales
            self.controller.load_initial_options()

            # Afficher un message de confirmation
            self.show_message("Réinitialisation", "L'application a été réinitialisée avec succès.")

    def return_to_main_menu(self):
        """Retourne au menu principal de sélection d'outils sans demander de confirmation."""
        # Importer ici pour éviter les imports circulaires
        import main

        # Créer et afficher la fenêtre de sélection d'outils
        self.tool_selection = main.ToolSelectionWindow(self.font_manager)
        self.tool_selection.show()

        # Fermer la fenêtre actuelle
        self.close()
