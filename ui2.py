from PyQt6.QtWidgets import QWidget, QVBoxLayout, QLabel, QComboBox, QPushButton, QMessageBox, QProgressBar,  QTableView,QDialog, QHBoxLayout, QSizePolicy, QFrame,QLineEdit, QFileDialog,QMenuBar,QMenu,QHeaderView,QFormLayout
from PyQt6.QtGui import QFont, QPixmap, QFontDatabase, QDoubleValidator

from PyQt6.QtGui import QPainter, QBrush, QRegion,QAction
from PyQt6.QtCore import QRect, QSize

from PyQt6.QtGui import QStandardItemModel, QStandardItem

from PyQt6.QtCore import Qt
from PyQt6.QtWidgets import QDateEdit
from PyQt6.QtCore import QDate
from controller import DataController
import os
import sys
import re #carractere authorisé

from tools.font_manager import FontManager

LEVIER_OBJECTIFS_MAP = {
    "MetaAds": ["Trafic", "Notoriété", "Vues de vidéo", "Vente","Interactions","Visites de profil"],
    "SnapchatAds": ["Trafic", "Notoriété", "Vues de vidéo","Vente"],
    "TiktokAds": ["Trafic", "Notoriété", "Vues de vidéo","Vente","Interactions","Followers"],
    "GoogleAds": ["Search", "Display"],
    "YoutubeAds": ["Search", "Preroll", "Bumper","Discovery","YT for Action"],
    "SpotifyAds": ["Trafic", "Notoriété", "Vues de vidéo","Streams"]
}



class MainWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.controller = DataController(self)  # Contrôleur pour gérer les requêtes
        self.font_manager = FontManager()
        self.init_ui()

    def init_ui(self):
        self.setWindowTitle("SA Corp - Saas Backoffice")
        self.setGeometry(100, 100, 1500, 700)
        self.setFont(self.font_manager.get_font("regular", 12))





        layout = QVBoxLayout()



        topnav_layout = QHBoxLayout()
        topnav_layout.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignTop)
        topnav_left_layout = QHBoxLayout()
        topnav_left_layout.setAlignment(Qt.AlignmentFlag.AlignTop)  # ✅ S'assurer que tout est aligné en haut
        self.account_widget = AccountWidget(self.font_manager)
        topnav_left_layout.addWidget(self.account_widget.get_layout())
        self.project_widget = ProjectWidget(self.font_manager)
        topnav_left_layout.addWidget(self.project_widget.get_layout())
        topnav_left_layout.addSpacing(20)  # Ajoute un peu d'espace entre les éléments
        topnav_layout.addLayout(topnav_left_layout)

        self.group_campaign_table = GroupCampaignTableWidget(self.font_manager,self.controller)
        topnav_layout.addWidget(self.group_campaign_table)



        layout.addLayout(topnav_layout)



        self.campaigns_table = CampaignsTableWidget(self.font_manager,self.controller)
        self.campaignsfilter = CampaignFiltersWidget(self.campaigns_table)
        layout.addWidget(self.campaignsfilter)
        layout.addWidget(self.campaigns_table)



        self.setLayout(layout)

        # Charger les premiers choix
        self.controller.load_initial_options()

        # Connecter le premier dropdown au changement
        self.account_widget.accounts_dropdown.currentIndexChanged.connect(
            lambda index: self.controller.update_projects_options() if self.account_widget.accounts_dropdown.count() > 0 else None
        )
        self.project_widget.projects_dropdown.currentIndexChanged.connect(self.controller.update_groupcampaigns_options)

        self.group_campaign_table.get_table().selectionModel().selectionChanged.connect(
            self.controller.update_campaigns_options
        )
        self.group_campaign_table.get_table().selectionModel().selectionChanged.connect(
            lambda: self.campaignsfilter.update_filters_from_table(self.campaigns_table.model)
        )

        #Account
        self.account_widget.add_account_button.clicked.connect(
            lambda: self.account_widget.open_add_account_window(self.controller)
        )
        self.account_widget.user_account_button.clicked.connect(
            lambda: self.account_widget.open_user_account_window(self.account_id_selected,self.account_name_selected,self.controller)
        )

        #project

        self.project_widget.add_project_button.clicked.connect(
            lambda: self.project_widget.open_add_project_window(self.account_id_selected, self.account_name_selected,self.controller)
        )
        #Group campaign
        self.group_campaign_table.add_group_button.clicked.connect(
            lambda: self.group_campaign_table.open_add_group_window(self.account_id_selected, self.account_name_selected,self.project_id_selected, self.project_name_selected,self.controller)
        )
        self.group_campaign_table.delete_group_button.clicked.connect(
            lambda: self.group_campaign_table.del_groupcampaign(self.project_id_selected)
        )
        self.group_campaign_table.modify_group_button.clicked.connect(
            lambda: self.group_campaign_table.open_modify_group_window(self.account_id_selected, self.account_name_selected,self.project_id_selected, self.project_name_selected,self.controller)
        )

        #campagne
        self.campaigns_table.create_button.clicked.connect(
            lambda: self.campaigns_table.open_add_campaign_window(self.account_id_selected, self.account_name_selected,self.project_id_selected, self.project_name_selected,self.group_id_selected,self.group_name_selected,self.controller)
        )

        self.campaigns_table.modify_button.clicked.connect(
            lambda: self.campaigns_table.open_modify_campaign_window(self.account_id_selected, self.account_name_selected,self.project_id_selected, self.project_name_selected,self.group_id_selected,self.group_name_selected,self.controller)
        )

        self.campaigns_table.view_button.clicked.connect(
            lambda: self.campaigns_table.open_view_campaign_window(self.account_id_selected, self.account_name_selected,self.project_id_selected, self.project_name_selected,self.group_id_selected,self.group_name_selected,self.controller)
        )







    def closeEvent(self, event):
        print("App fermée proprement")
        # Arrêter les timers, threads, sauvegarder l’état, etc.
        event.accept()

    def show_message(self, title, message):


        """Affiche un message à l'utilisateur."""
        QMessageBox.information(self, title, message)





class AccountWidget(QWidget):
    def __init__(self,font_manager):
        super().__init__()

        self.font_manager = font_manager
        self.setFont(self.font_manager.get_font("regular", 12))

        self.frame = QFrame(self)
        self.frame.setObjectName("accountFrame")
        self.frame.setFixedSize(320, 100)  # Même taille que AccountWidget
        self.frame.setStyleSheet("""
            QFrame#accountFrame {
                border: 1px solid gray;
                border-radius: 10px;
            }
        """)

        # Layout principal pour l'ensemble des infos Account
        self.account_main_layout = QHBoxLayout(self.frame)  # ⬅️ PAS de self.setLayout() ici
        self.account_main_layout.setContentsMargins(10, 5, 10, 5)  # La méthode setContentsMargins(left, top, right, bottom
        self.account_main_layout.setSpacing(10)


        # Layout vertical pour les infos du compte (label + dropdown)
        account_info_layout = QVBoxLayout()
        # account_info_layout.setSpacing(0)
        account_info_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)  # Aligner à gauche

        # Label pour sélectionner un compte
        self.label1 = QLabel("Selected Account :")
        self.label1.setFont(self.font_manager.get_font("bold", 16))
        account_info_layout.addWidget(self.label1)

        # Dropdown pour choisir un compte
        self.accounts_dropdown = QComboBox()
        self.accounts_dropdown.setFixedSize(200, 40)
        account_info_layout.addWidget(self.accounts_dropdown)
        account_info_layout.addSpacing(0)

        account_button_layout = QHBoxLayout()
        self.add_account_button = QPushButton("Add Account")
        self.add_account_button.setFixedSize(100, self.add_account_button.sizeHint().height())
        self.user_account_button = QPushButton("View users")
        self.user_account_button.setFixedSize(100, self.user_account_button.sizeHint().height())

        account_button_layout.addWidget(self.add_account_button)
        account_button_layout.addWidget(self.user_account_button)

        # Ajouter ce layout vertical au layout horizontal
        account_info_layout.addLayout(account_button_layout)
        self.account_main_layout.addLayout(account_info_layout)


        # Ajout de l'image du compte (à droite)
        self.account_image = QLabel(self)
        self.account_image.setFixedSize(70, 70)
        apply_rounded_mask(self.account_image, radius=15)
        self.account_image.setScaledContents(True)


        # Ajouter l'image à droite
        self.account_main_layout.addWidget(self.account_image, alignment=Qt.AlignmentFlag.AlignRight)



    def get_layout(self):
        """Retourne le layout pour être ajouté ailleurs"""
        return self.frame

    def open_add_account_window(self,controller):
        """Ouvre une nouvelle fenêtre pour ajouter un projet"""
        dialog = AddAccountDialog(controller,self.font_manager)
        dialog.exec()  #  Affiche la boîte de dialogue en mode bloquant

    def open_user_account_window(self,account_id,account_name, controller):
        """Ouvre une nouvelle fenêtre pour ajouter un projet"""
        dialog = UserAccountDialog(account_id,account_name,controller,self.font_manager)
        dialog.exec()  #  Affiche la boîte de dialogue en mode bloquant



class ProjectWidget(QWidget):
    def __init__(self,font_manager):
        super().__init__()

        # Encapsulation dans un `QFrame`
        self.font_manager = font_manager
        self.frame = QFrame(self)
        self.frame.setFont(self.font_manager.get_font("regular", 12))
        self.frame.setObjectName("projectFrame")
        self.frame.setFixedSize(320, 90)  # Même taille que AccountWidget
        self.frame.setStyleSheet("""
            QFrame#projectFrame {
                border: 1px solid gray;
                border-radius: 10px;
            }
        """)

        # Layout principal horizontal
        self.project_main_layout = QHBoxLayout(self.frame)
        self.project_main_layout.setContentsMargins(10, 5, 10, 5)  # Marges internes
        self.project_main_layout.setSpacing(10)  # Espacement entre éléments

        # Layout vertical pour les infos du projet (label + dropdown)
        project_info_layout = QVBoxLayout()
        project_info_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)  # Aligner à gauche

        # Label pour sélectionner un projet
        self.label2 = QLabel("Selected project :")
        self.label2.setFont(self.font_manager.get_font("bold", 16))
        project_info_layout.addWidget(self.label2)

        # Dropdown pour choisir un projet
        self.projects_dropdown = QComboBox()
        self.projects_dropdown.setFixedSize(200, 40)
        self.projects_dropdown.setEnabled(False)  # Désactivé initialement
        self.projects_dropdown.setEditable(True)  #  Permet de centrer le texte
        self.projects_dropdown.lineEdit().setAlignment(Qt.AlignmentFlag.AlignCenter)  #  Centre le texte
        self.projects_dropdown.setEditable(False)
        project_info_layout.addWidget(self.projects_dropdown)

        project_info_layout.addSpacing(20)

        self.add_project_button = QPushButton("Add Project")
        self.add_project_button.setFixedSize(100, self.add_project_button.sizeHint().height())
        project_info_layout.addWidget(self.add_project_button)
        self.add_project_button.setEnabled(False)
        # self.add_project_button.clicked.connect(self.open_add_project_window)

        # Ajouter le layout vertical dans le layout horizontal
        self.project_main_layout.addLayout(project_info_layout)

        # Ajout de l'image du projet (à droite)
        self.project_image = QLabel(self)
        self.project_image.setFixedSize(70, 70)
        apply_rounded_mask(self.project_image, radius=15)
        self.project_image.setScaledContents(True)
        self.project_image.setEnabled(False)

        # Ajouter l'image à droite
        self.project_main_layout.addWidget(self.project_image, alignment=Qt.AlignmentFlag.AlignRight)

    def get_layout(self):
        """Retourne le `QFrame` pour être ajouté ailleurs"""
        return self.frame  # On retourne `self.frame` au lieu du layout

    def open_add_project_window(self,account_id_selected,account_name_selected,controller):
        """Ouvre une nouvelle fenêtre pour ajouter un projet"""
        dialog = AddProjectDialog(account_id_selected,account_name_selected,controller,self.font_manager)
        dialog.exec()  #  Affiche la boîte de dialogue en mode bloquant


class GroupCampaignTableWidget(QWidget):
    def __init__(self,font_manager,controller):
        super().__init__()

        self.controller = controller
        self.font_manager = font_manager
        # Création du modèle avec colonnes
        self.model = QStandardItemModel(0, 6)  # 0 lignes, 6 colonnes
        self.model.setHorizontalHeaderLabels(["ID", "Date de création", "Nom du groupe",
                                              "nb campagnes", "Budget margé", "Budget non margé"])




        self.locked_columns = [0, 1, 3, 4, 5]  #  Colonnes verrouillées (ID, Date de création, Nombre de campagnes)
        self.modified_rows = set()  #  Stocke les lignes modifiées



        # Création du tableau
        self.table = QTableView()
        self.table.setSortingEnabled(True)  #  Active le tri au clic
        self.table.setModel(self.model)
        self.table.setFixedSize(800, 150)


        # Activer la sélection de toute la ligne
        self.table.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.table.setSelectionMode(QTableView.SelectionMode.SingleSelection)
        self.table.setStyleSheet("""
            QTableView::item:selected {
                background-color: #476AFF;
                color: white;
            }
        """)

        column_widths = {
            0: 50,   # ID
            1: 100,  # Date de création
            2: 250,  # Nom du groupe
            3: 90,  # Nombre de campagnes
            4: 120,  # Budget margé
            5: 120   # Budget non margé
        }
        for col_index, width in column_widths.items():
            self.table.setColumnWidth(col_index, width)



        # Connecter le signal pour détecter les modifications
        self.model.dataChanged.connect(self.highlight_modified_row)

        main_layout = QHBoxLayout()

        # 1. Ajouter la table dans une VBox pour le redimensionnement vertical
        table_layout = QVBoxLayout()
        table_layout.addWidget(self.table)
        # Layout principal
        self.layout = QVBoxLayout()
        self.layout.addWidget(self.table)


        self.group_button_layout = QVBoxLayout()
        self.group_button_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.add_group_button = QPushButton("Créer")
        self.add_group_button.setEnabled(False)
        self.delete_group_button = QPushButton("Supprimer")
        self.delete_group_button.setEnabled(False)
        self.modify_group_button = QPushButton("Modifier")
        self.modify_group_button.setEnabled(False)

        self.group_button_layout.addWidget(self.add_group_button)
        self.group_button_layout.addWidget(self.delete_group_button)
        self.group_button_layout.addWidget(self.modify_group_button)

        main_layout.addLayout(table_layout)
        main_layout.addLayout(self.group_button_layout)
        self.setLayout(main_layout)
        self.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Maximum)




    def update_data(self, data):
        """ Met à jour les données du tableau en gardant les colonnes verrouillées"""
        self.model.removeRows(0, self.model.rowCount())  #  Supprime les anciennes lignes

        for row, values in enumerate(data):
            for col, value in enumerate(values):
                item = QStandardItem(str(value))
                if isinstance(value, int):  #  Permet un tri correct des nombres
                    item.setData(value, Qt.ItemDataRole.EditRole)

                #  Désactiver l'édition pour les colonnes verrouillées
                if col in self.locked_columns:
                    item.setFlags(item.flags() & ~Qt.ItemFlag.ItemIsEditable)

                self.model.setItem(row, col, item)

    def highlight_modified_row(self, topLeft, bottomRight, roles):
        """ Met en surbrillance une ligne lorsque des données sont modifiées"""
        if Qt.ItemDataRole.EditRole in roles:
            row = topLeft.row()
            if row not in self.modified_rows:
                self.modified_rows.add(row)  #  Ajouter la ligne aux lignes modifiées
                for col in range(self.model.columnCount()):
                    item = self.model.item(row, col)
                    item.setBackground(Qt.GlobalColor.darkMagenta)  #  Couleur normale pour ligne modifiée
                      #  Texte en noir pour bien voir

                    #  Modifier la couleur si la ligne est sélectionnée
                    self.table.setStyleSheet(f"""
                        QTableView::item:selected {{
                            background-color: #FF8C00;  /*  Orange foncé uniquement pour les lignes modifiées */
                            color: white;
                        }}
                    """)
    def get_table(self):
        """Retourne le QTableView pour être ajouté ailleurs"""
        return self.table


    def open_add_group_window(self,account_id_selected,account_name_selected,project_id_selected,project_name_selected,controller):
        """Ouvre une nouvelle fenêtre pour ajouter un projet"""
        dialog = AddGroupCampaignDialog(account_id_selected,account_name_selected,project_id_selected,project_name_selected,controller,self.font_manager)
        dialog.exec()  #  Affiche la boîte de dialogue en mode bloquant

    def open_modify_group_window(self,account_id_selected,account_name_selected,project_id_selected,project_name_selected,controller):
        """Ouvre une nouvelle fenêtre pour ajouter un projet"""
        selection_model = self.table.selectionModel()
        selected_rows = selection_model.selectedRows()
        if not selected_rows:
            QMessageBox.warning(self, "Erreur", "Veuillez sélectionner un groupe de campagne.")
            return

        selected_index = selected_rows[0]
        groupcampaing_id = int(self.model.item(selected_index.row(), 0).text())
        groupcampaing_name = self.model.item(selected_index.row(), 2).text()
        dialog = ModifyGroupCampaignDialog(account_id_selected,account_name_selected,project_id_selected,project_name_selected,controller,self.font_manager,groupcampaing_id,groupcampaing_name)
        dialog.exec()  #  Affiche la boîte de dialogue en mode bloquant


    def del_groupcampaign(self,project_id_selected):
        """Vérifie les données et envoie le projet via le contrôleur."""
        # Vérification via le contrôleur

        selection_model = self.table.selectionModel()
        selected_rows = selection_model.selectedRows()
        if not selected_rows:
            msg_box = QMessageBox()
            msg_box.setWindowTitle("Erreur")
            msg_box.setText("Veuillez sélectionner un groupe de campagne.")
            # Définir une icône personnalisée
            if getattr(sys, 'frozen', False):
                base_path = sys._MEIPASS
                assets_path = os.path.join(base_path,"assets","icons")
            else:
                assets_path = os.path.abspath("assets/icons/")

            icon_path = os.path.join(assets_path, "custom_echec.png")  # Ton chemin d'icône ici
            msg_box.setIconPixmap(QPixmap(icon_path).scaled(48, 48, Qt.AspectRatioMode.KeepAspectRatio))
            msg_box.exec()

            return
        confirmation = QMessageBox.question(
            self,
            "Confirmation",
            "Êtes-vous sûr de vouloir supprimer ce groupe de campagne ?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        if confirmation != QMessageBox.StandardButton.Yes:
            return

        selected_index = selected_rows[0]
        groupcampaign_id = int(self.model.item(selected_index.row(), 0).text())
        print("selected index id : ", selected_index)



        print("group id : ", groupcampaign_id)
        print("projet id : ", project_id_selected)

        success, message = self.controller.del_groupcampaign_to_xano(project_id_selected, groupcampaign_id)
        print(message)

        if success:
            msg_box = QMessageBox()
            msg_box.setWindowTitle("Succès")
            msg_box.setText("Groupe de campagne supprimé avec succès.")
            # Définir une icône personnalisée
            if getattr(sys, 'frozen', False):
                base_path = sys._MEIPASS
                assets_path = os.path.join(base_path,"assets","icons")
            else:
                assets_path = os.path.abspath("assets/icons/")

            icon_path = os.path.join(assets_path, "custom_success.png")
            msg_box.setIconPixmap(QPixmap(icon_path).scaled(48, 48, Qt.AspectRatioMode.KeepAspectRatio))
            msg_box.exec()
            # QMessageBox.information(self, "Succès", "Groupe de campagne supprimé avec succès.")

        else:
            msg_box = QMessageBox()
            msg_box.setWindowTitle("Echec")
            msg_box.setText(f"Échec de suppression : {message}")
            if getattr(sys, 'frozen', False):
                base_path = sys._MEIPASS
                assets_path = os.path.join(base_path,"assets","icons")
            else:
                assets_path = os.path.abspath("assets/icons/")

            icon_path = os.path.join(assets_path, "custom_echec.png")
            msg_box.setIconPixmap(QPixmap(icon_path).scaled(48, 48, Qt.AspectRatioMode.KeepAspectRatio))
            msg_box.exec()

        self.controller.update_groupcampaigns_options()



class CampaignsTableWidget(QWidget):
    def __init__(self,font_manager,controller):
        super().__init__()

        self.font_manager = font_manager
        self.controller = controller
        # Création du modèle avec colonnes
        self.model = QStandardItemModel(0, 6)  # 0 lignes, 6 colonnes
        self.model.setHorizontalHeaderLabels(["ID","Nom de la campagne",
                                              "levier", "Objectif","Budget margé", "Budget non margé","State","Start Date","End Date","ID Admin account","ID Ad Account","ID Campaign"])




        self.locked_columns = [0]  #  Colonnes verrouillées (ID, Date de création, Nombre de campagnes)
        self.modified_rows = set()  #  Stocke les lignes modifiées



        # Création du tableau
        self.table = QTableView()
        self.table.setSortingEnabled(True)  #  Active le tri au clic
        self.table.setModel(self.model)
        # self.table.setFixedSize(800, 150)


        # Activer la sélection de toute la ligne
        self.table.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.table.setSelectionMode(QTableView.SelectionMode.SingleSelection)
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.table.setStyleSheet("""
            QTableView::item:selected {
                background-color: #476AFF;
                color: white;
            }
        """)


        # column_widths = {
        #     0: 50,   # ID
        #     # 1: 100,  # Date de création
        #     # 2: 250,  # Nom du groupe
        #     # 3: 90,  # Nombre de campagnes
        #     # 4: 120,  # Budget margé
        #     # 5: 120   # Budget non margé
        # }
        # for col_index, width in column_widths.items():
        #     self.table.setColumnWidth(col_index, width)



        # Connecter le signal pour détecter les modifications
        self.model.dataChanged.connect(self.highlight_modified_row)

        # Layout principal
        mainlayout = QVBoxLayout()


        # Add buttons for "Creer", "Modifier", "Supprimer"
        button_layout = QHBoxLayout()
        button_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.create_button = QPushButton("Creer")
        self.create_button.setMaximumWidth(100)
        self.create_button.setEnabled(False)

        self.modify_button = QPushButton("Modifier")
        self.modify_button.setMaximumWidth(100)
        self.modify_button.setEnabled(False)

        self.view_button = QPushButton("View")
        self.view_button.setMaximumWidth(100)
        self.view_button.setEnabled(False)

        self.delete_button = QPushButton("Supprimer")
        self.delete_button.setMaximumWidth(100)
        self.delete_button.setEnabled(False)

        button_layout.addWidget(self.create_button)
        button_layout.addWidget(self.modify_button)
        button_layout.addWidget(self.view_button)
        button_layout.addWidget(self.delete_button)

        mainlayout.addLayout(button_layout)
        mainlayout.addWidget(self.table)




        self.setLayout(mainlayout)




    def update_data(self, data):
        """ Met à jour les données du tableau en gardant les colonnes verrouillées"""
        self.model.removeRows(0, self.model.rowCount())  #  Supprime les anciennes lignes

        for row, values in enumerate(data):
            for col, value in enumerate(values):
                item = QStandardItem(str(value))
                if isinstance(value, int):  #  Permet un tri correct des nombres
                    item.setData(value, Qt.ItemDataRole.EditRole)

                #  Désactiver l'édition pour les colonnes verrouillées
                if col in self.locked_columns:
                    item.setFlags(item.flags() & ~Qt.ItemFlag.ItemIsEditable)

                self.model.setItem(row, col, item)



    def highlight_modified_row(self, topLeft, bottomRight, roles):
        """ Met en surbrillance une ligne lorsque des données sont modifiées"""
        if Qt.ItemDataRole.EditRole in roles:
            row = topLeft.row()
            if row not in self.modified_rows:
                self.modified_rows.add(row)  #  Ajouter la ligne aux lignes modifiées
                for col in range(self.model.columnCount()):
                    item = self.model.item(row, col)
                    item.setBackground(Qt.GlobalColor.darkMagenta)  #  Couleur normale pour ligne modifiée
                      #  Texte en noir pour bien voir

                    #  Modifier la couleur si la ligne est sélectionnée
                    self.table.setStyleSheet(f"""
                        QTableView::item:selected {{
                            background-color: #FF8C00;  /*  Orange foncé uniquement pour les lignes modifiées */
                            color: white;
                        }}
                    """)


    def get_table(self):
        """Retourne le QTableView pour être ajouté ailleurs"""
        return self.table

    def open_add_campaign_window(self,account_id_selected,account_name_selected,project_id_selected,project_name_selected,group_id_selected,group_name_selected,controller):
        """Ouvre une nouvelle fenêtre pour ajouter un projet"""
        dialog = AddCampaignDialog(account_id_selected,account_name_selected,project_id_selected,project_name_selected,group_id_selected,group_name_selected,controller,self.font_manager)
        dialog.exec()  #  Affiche la boîte de dialogue en mode bloquant

    def open_modify_campaign_window(self,account_id_selected,account_name_selected,project_id_selected,project_name_selected,group_id_selected,group_name_selected,controller):
        """Ouvre une nouvelle fenêtre pour ajouter un projet"""
        selection_model = self.table.selectionModel()
        selected_rows = selection_model.selectedRows()
        if not selected_rows:
            QMessageBox.warning(self, "Erreur", "Veuillez sélectionner une campagne.")
            return

        selected_index = selected_rows[0]
        campaign_id = int(self.model.item(selected_index.row(), 0).text())
        print("selected campaign id : ", campaign_id)

        dialog = AddCampaignDialog(account_id_selected,account_name_selected,project_id_selected,project_name_selected,group_id_selected,group_name_selected,controller,self.font_manager,campaign_id=campaign_id)
        dialog.exec()  #  Affiche la boîte de dialogue en mode bloquant

    def open_view_campaign_window(self,account_id_selected,account_name_selected,project_id_selected,project_name_selected,group_id_selected,group_name_selected,controller):
        """Ouvre une nouvelle fenêtre pour ajouter un projet"""
        selection_model = self.table.selectionModel()
        selected_rows = selection_model.selectedRows()
        if not selected_rows:
            QMessageBox.warning(self, "Erreur", "Veuillez sélectionner une campagne.")
            return

        selected_index = selected_rows[0]
        campaign_id = int(self.model.item(selected_index.row(), 0).text())
        print("selected campaign id : ", campaign_id)

        dialog = AddCampaignDialog(account_id_selected,account_name_selected,project_id_selected,project_name_selected,group_id_selected,group_name_selected,controller,self.font_manager,campaign_id=campaign_id,read_only=True)
        dialog.exec()  #  Affiche la boîte de dialogue en mode bloquant



class CampaignFiltersWidget(QWidget):
    def __init__(self, campaigns_table_widget):
        super().__init__()
        self.campaigns_table_widget = campaigns_table_widget
        self.layout = QHBoxLayout()
        self.layout.setSpacing(10)

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Rechercher une campagne")
        self.layout.addWidget(self.search_input)

        self.levier_filter = QComboBox()
        self.levier_filter.addItem("Levier")
        self.layout.addWidget(self.levier_filter)

        self.objectif_filter = QComboBox()
        self.objectif_filter.addItem("Objectif")
        self.layout.addWidget(self.objectif_filter)

        self.status_filter = QComboBox()
        self.status_filter.addItem("Statut")
        self.layout.addWidget(self.status_filter)

        self.start_date_filter = QDateEdit()
        self.start_date_filter.setCalendarPopup(True)
        self.start_date_filter.setButtonSymbols(QDateEdit.ButtonSymbols.NoButtons)
        self.start_date_filter.setDisplayFormat("yyyy-MM-dd")
        self.start_date_filter.setSpecialValueText("Date de début")
        self.start_date_filter.setDate(QDate.currentDate())
        self.layout.addWidget(self.start_date_filter)
        self.search_input.setStyleSheet("""
            QLineEdit {
                outline: none;
                border: 1px solid gray;
                border-radius: 5px;
                padding: 2px;
            }
            QLineEdit:focus {
                border: 1px solid #888;
            }
        """)
        self.start_date_filter.setStyleSheet("""
            QDateEdit {
                outline: none;
                border: 1px solid gray;
                border-radius: 5px;
                padding: 2px;
            }
            QDateEdit:focus {
                border: 1px solid #888;
            }

            QCalendarWidget QWidget {
                background-color: white;
                color: black;

            }

            QCalendarWidget QToolButton {
                background-color: none;
                color: black;
                font-weight: bold;
                border: none;
                margin: 5px;
            }

            QCalendarWidget QToolButton:hover {
                background-color: #eeeeee;
            }

            QCalendarWidget QSpinBox {
                width: 70px;
                font-size: 12px;
                color: black;
                background-color: none;
                border: none;
            }

            QCalendarWidget QMenu {
                background-color: white;
                border: 1px solid gray;
            }

            QCalendarWidget QAbstractItemView {
                selection-background-color: #476AFF;
                selection-color: white;
                background-color: #fefefe;
                gridline-color: lightgray;
            }

            QCalendarWidget QHeaderView::section {
                background-color: #476AFF;
                color: white;
                font-weight: bold;
            }

            QCalendarWidget QAbstractItemView::item {
                color: black;
            }

            QCalendarWidget QAbstractItemView::item:enabled:nth-child(7),  /* Saturday */
            QCalendarWidget QAbstractItemView::item:enabled:nth-child(1)   /* Sunday */
            {
                color: #476AFF;
            }
            QCalendarWidget QToolButton#qt_calendar_prevmonth {
                qproperty-icon: url("assets/icons/arrow-circle-left.png");
                qproperty-iconSize: 20px 20px;
            }

            QCalendarWidget QToolButton#qt_calendar_nextmonth {
                qproperty-icon: url("assets/icons/arrow-circle-right.png");
                qproperty-iconSize: 20px 20px;
            }


        """)
        self.start_date_filter.lineEdit().setReadOnly(True)
        self.start_date_filter.lineEdit().setCursor(Qt.CursorShape.ArrowCursor)
        self.reset_button = QPushButton("Réinitialiser")
        self.reset_button.clicked.connect(self.reset_filters)
        self.layout.addWidget(self.reset_button)
        self.setLayout(self.layout)

        self.search_input.textChanged.connect(self.filter_table)
        self.start_date_filter.dateChanged.connect(self.filter_table)
        self.levier_filter.currentIndexChanged.connect(self.filter_table)
        self.objectif_filter.currentIndexChanged.connect(self.filter_table)
        self.status_filter.currentIndexChanged.connect(self.filter_table)


    def update_filters_from_table(self, model: QStandardItemModel):
        self._populate_filter(self.levier_filter, model, 2,"Levier")
        self._populate_filter(self.objectif_filter, model, 3,'Objectif')
        self._populate_filter(self.status_filter, model, 6,"Statut")

    def _populate_filter(self, combo: QComboBox, model: QStandardItemModel, column: int,text):
        existing_values = set()
        for row in range(model.rowCount()):
            value = model.item(row, column).text()
            if value not in existing_values:
                existing_values.add(value)
        combo.clear()
        combo.addItem(text)
        combo.addItems(sorted(existing_values))

    def get_layout(self):
        return self.layout

    def filter_table(self):
        model = self.campaigns_table_widget.model
        search_text = self.search_input.text().lower()
        selected_start_date = self.start_date_filter.date().toString("yyyy-MM-dd")

        for row in range(model.rowCount()):
            show_row = True
            if self.levier_filter.currentIndex() > 0:
                val = model.item(row, 2).text()
                if val != self.levier_filter.currentText():
                    show_row = False
            if self.objectif_filter.currentIndex() > 0:
                val = model.item(row, 3).text()
                if val != self.objectif_filter.currentText():
                    show_row = False
            if self.status_filter.currentIndex() > 0:
                val = model.item(row, 6).text()
                if val != self.status_filter.currentText():
                    show_row = False
            if search_text:
                campaign_name = model.item(row, 1).text().lower()
                if search_text not in campaign_name:
                    show_row = False
            # Compare start date only if selected date is not today's date
            if self.start_date_filter.date() != QDate.currentDate():
                row_start_date = model.item(row, 7).text()
                if row_start_date < selected_start_date:
                    show_row = False

            self.campaigns_table_widget.get_table().setRowHidden(row, not show_row)

    def reset_filters(self):
        self.search_input.clear()
        self.levier_filter.setCurrentIndex(0)
        self.objectif_filter.setCurrentIndex(0)
        self.status_filter.setCurrentIndex(0)
        self.start_date_filter.setDate(QDate.currentDate())


class AddAccountDialog(QDialog):
    def __init__(self, controller,font_manager):
        super().__init__()

        self.controller = controller

        self.setWindowTitle("Creer un nouveau Compte")
        self.setFixedSize(500, 400)

        layout = QVBoxLayout()

        # Label et champ pour le nom du projet
        self.label2 = QLabel("Creer un nouveau compte :")


        self.label2.setFont(font_manager.get_font("bold", 14))


        self.label4 = QLabel("Nom du Compte :")
        self.label4.setFont(font_manager.get_font("bold", 12))
        self.label42 = QLabel()
        self.account_name_input = QLineEdit()

        row1_layout = QHBoxLayout()
        row1_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)  #  Alignement à gauche
        row1_layout.addWidget(self.label2)



        row3_layout = QVBoxLayout()
        row3_layout.addWidget(self.label4)
        row3_layout.addWidget(self.account_name_input)

        row3_layout.addWidget(self.label42)



        layout.addLayout(row1_layout)
        layout.addLayout(row3_layout)

        # Bouton pour sélectionner une image
        self.image_button = QPushButton("Charger une image")
        self.image_button.clicked.connect(self.load_image)
        layout.addWidget(self.image_button)

        # Label pour afficher l’aperçu de l’image
        self.image_preview = QLabel()
        self.image_preview.setFixedSize(150, 150)  # Taille de l’aperçu
        self.image_preview.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.image_preview.setStyleSheet("border: 1px solid gray;")  # Ajouter une bordure
        # Créer un layout centré pour l’image
        image_layout = QHBoxLayout()
        image_layout.addStretch()  # Ajoute un espace flexible à gauche
        image_layout.addWidget(self.image_preview, alignment=Qt.AlignmentFlag.AlignCenter)  # Ajoute l'image centrée
        image_layout.addStretch()  # Ajoute un espace flexible à droite

        layout.addLayout(image_layout)

        self.label5 = QLabel()
        layout.addWidget(self.label5)





        # Bouton d'ajout
        self.submit_button = QPushButton("Créer le compte")
        self.submit_button.clicked.connect(self.post_account)  #  Ferme la fenêtre sur validation
        layout.addWidget(self.submit_button)
        self.setLayout(layout)

    def load_image(self):
        """Ouvre un QFileDialog pour sélectionner une image et vérifie si elle est carrée"""
        file_dialog = QFileDialog()
        file_dialog.setNameFilters(["Images (*.png *.jpg *.jpeg)"])
        file_dialog.setViewMode(QFileDialog.ViewMode.List)



        if file_dialog.exec():
            selected_files = file_dialog.selectedFiles()
            if selected_files:
                self.image_path = selected_files[0]  # Stocke le chemin de l’image
                file_size = os.path.getsize(self.image_path)  # Récupère la taille en octets

                max_size = 2000 * 1024  # 500 KB en octets
                pixmap = QPixmap(self.image_path)
                self.label5.setText("")

                # Vérifie si l'image est bien chargée
                if not pixmap.isNull():
                    width = pixmap.width()
                    height = pixmap.height()

                    if file_size > max_size:
                        self.label5.setText("L'image est trop lourde (max 2 MB).")
                        self.label5.setStyleSheet("color: #FF6464;")
                        self.image_preview.clear()
                        self.image_path = None
                        return

                    # Vérifie que l'image est carrée
                    if width != height:
                        self.label5.setText("L'image doit être carrée.")
                        self.label5.setStyleSheet("color: #FF6464;")
                        self.image_preview.clear()
                        self.image_path = None
                        return

                    # Vérifie que la taille est comprise entre 100x100 et 400x400 px
                    if width < 100 or width > 1000:
                        self.label5.setText("L'image doit être entre 100x100 et 1000x1000 pixels.")
                        self.label5.setStyleSheet("color: #FF6464;")
                        self.image_preview.clear()
                        self.image_path = None
                        return

                    # Si tout est valide, affiche l'image
                    scaled_pixmap = pixmap.scaled(200, 200, Qt.AspectRatioMode.KeepAspectRatio)
                    self.image_preview.setPixmap(scaled_pixmap)
                    self.label5.setText("")  # Efface le message d'erreur

                else:
                    self.label5.setText("Impossible de charger l'image.")
                    self.label5.setStyleSheet("color: #FF6464;")  # Message en rouge

    def post_account(self):
        """Vérifie les données et envoie le projet via le contrôleur."""
        account_name = self.account_name_input.text().strip()

        # Vérification via le contrôleur
        is_valid, error_message = self.controller.validate_project_name(account_name)
        if not is_valid:
            self.label42.setText(error_message)
            self.label42.setStyleSheet("color: #FF6464;")
            return

        # Envoi des données via le contrôleur
        self.image_path = self.image_path if hasattr(self, 'image_path') and self.image_path else None

        success, message = self.controller.send_account_to_xano(account_name, self.image_path)

        if success:
            QMessageBox.information(self, "Succès", "Compte ajouté avec succès.")
            self.controller.update_account_options()
            self.accept()  # Ferme la boîte de dialogue
        else:
            QMessageBox.warning(self, "Erreur", f"Échec de l'ajout du compte : {message}")


class UserAccountDialog(QDialog):
    def __init__(self,account_id,account_name, controller,font_manager):
        super().__init__()

        self.controller = controller
        self.account_id = account_id
        self.account_name = account_name



        self.setWindowTitle("Utilisateur")
        self.setFixedSize(800, 400)

        layout = QVBoxLayout()

        # Label et champ pour le nom du projet
        # Label et champ pour le nom du projet
        self.label2 = QLabel("Account :")
        self.label22 = QLabel(str(account_name))
        self.label3 = QLabel("- ID Xano:")
        self.label32 = QLabel(str(account_id))

        self.label2.setFont(font_manager.get_font("bold", 14))
        self.label22.setFont(font_manager.get_font("regular", 14))
        self.label3.setFont(font_manager.get_font("bold", 14))
        self.label32.setFont(font_manager.get_font("regular", 14))




        row1_layout = QHBoxLayout()
        row1_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)  #  Alignement à gauche
        row1_layout.addWidget(self.label2)
        row1_layout.addWidget(self.label22)

        row1_layout.addWidget(self.label3)
        row1_layout.addWidget(self.label32)


        layout.addLayout(row1_layout)



        # Création du modèle avec colonnes
        self.model = QStandardItemModel(0, 6)  # 0 lignes, 6 colonnes
        self.model.setHorizontalHeaderLabels(["ID", "Email", "Nom", "Prénom", "Entreprise", "Rôle"])

        # Création du tableau
        self.table = QTableView()
        self.table.setSortingEnabled(True)  # Active le tri au clic
        self.table.setModel(self.model)
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.table.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.table.setSelectionMode(QTableView.SelectionMode.SingleSelection)
        self.table.setStyleSheet("""
            QTableView::item:selected {
            background-color: #476AFF;
            color: white;
            }
        """)

        # Layout principal
        table_layout = QVBoxLayout()
        table_layout.addWidget(self.table)
        layout.addLayout(table_layout)
        self.controller.update_user_options(self.account_id,self.model)






        # Bouton d'ajout
        self.quit_button = QPushButton("Quitter")
        self.quit_button.clicked.connect(self.accept)  # Ferme la fenêtre sur validation
        layout.addWidget(self.quit_button)
        self.setLayout(layout)

    # def load_image(self):
    #     """Ouvre un QFileDialog pour sélectionner une image et vérifie si elle est carrée"""
    #     file_dialog = QFileDialog()
    #     file_dialog.setNameFilters(["Images (*.png *.jpg *.jpeg)"])
    #     file_dialog.setViewMode(QFileDialog.ViewMode.List)



    #     if file_dialog.exec():
    #         selected_files = file_dialog.selectedFiles()
    #         if selected_files:
    #             self.image_path = selected_files[0]  # Stocke le chemin de l’image
    #             file_size = os.path.getsize(self.image_path)  # Récupère la taille en octets

    #             max_size = 2000 * 1024  # 500 KB en octets
    #             pixmap = QPixmap(self.image_path)
    #             self.label5.setText("")

    #             # Vérifie si l'image est bien chargée
    #             if not pixmap.isNull():
    #                 width = pixmap.width()
    #                 height = pixmap.height()

    #                 if file_size > max_size:
    #                     self.label5.setText("L'image est trop lourde (max 2 MB).")
    #                     self.label5.setStyleSheet("color: #FF6464;")
    #                     self.image_preview.clear()
    #                     self.image_path = None
    #                     return

    #                 # Vérifie que l'image est carrée
    #                 if width != height:
    #                     self.label5.setText("L'image doit être carrée.")
    #                     self.label5.setStyleSheet("color: #FF6464;")
    #                     self.image_preview.clear()
    #                     self.image_path = None
    #                     return

    #                 # Vérifie que la taille est comprise entre 100x100 et 400x400 px
    #                 if width < 100 or width > 1000:
    #                     self.label5.setText("L'image doit être entre 100x100 et 1000x1000 pixels.")
    #                     self.label5.setStyleSheet("color: #FF6464;")
    #                     self.image_preview.clear()
    #                     self.image_path = None
    #                     return

    #                 # Si tout est valide, affiche l'image
    #                 scaled_pixmap = pixmap.scaled(200, 200, Qt.AspectRatioMode.KeepAspectRatio)
    #                 self.image_preview.setPixmap(scaled_pixmap)
    #                 self.label5.setText("")  # Efface le message d'erreur

    #             else:
    #                 self.label5.setText("Impossible de charger l'image.")
    #                 self.label5.setStyleSheet("color: #FF6464;")  # Message en rouge

    def post_account(self):
        """Vérifie les données et envoie le projet via le contrôleur."""
        account_name = self.account_name_input.text().strip()

        # Vérification via le contrôleur
        is_valid, error_message = self.controller.validate_project_name(account_name)
        if not is_valid:
            self.label42.setText(error_message)
            self.label42.setStyleSheet("color: #FF6464;")
            return

        # Envoi des données via le contrôleur
        self.image_path = self.image_path if hasattr(self, 'image_path') and self.image_path else None

        success, message = self.controller.send_account_to_xano(account_name, self.image_path)

        if success:
            QMessageBox.information(self, "Succès", "Compte ajouté avec succès.")
            self.controller.update_account_options()
            self.accept()  # Ferme la boîte de dialogue
        else:
            QMessageBox.warning(self, "Erreur", f"Échec de l'ajout du compte : {message}")



class AddProjectDialog(QDialog):
    def __init__(self,account_id,account_name,controller,font_manager):
        super().__init__()

        self.controller = controller
        self.account_id = account_id
        self.account_name = account_name

        self.setWindowTitle("Creer un nouveau Projet")
        self.setFixedSize(500, 400)

        layout = QVBoxLayout()

        # Label et champ pour le nom du projet
        self.label2 = QLabel("Account :")
        self.label22 = QLabel(str(account_name))
        self.label3 = QLabel("- ID Xano:")
        self.label32 = QLabel(str(account_id))

        self.label2.setFont(font_manager.get_font("bold", 14))
        self.label22.setFont(font_manager.get_font("regular", 14))
        self.label3.setFont(font_manager.get_font("bold", 14))
        self.label32.setFont(font_manager.get_font("regular", 14))

        self.label4 = QLabel("Nom du projet :")
        self.label4.setFont(font_manager.get_font("bold", 12))
        self.label42 = QLabel()
        self.project_name_input = QLineEdit()

        row1_layout = QHBoxLayout()
        row1_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)  #  Alignement à gauche
        row1_layout.addWidget(self.label2)

        row1_layout.addWidget(self.label22)

        row1_layout.addWidget(self.label3)
        row1_layout.addWidget(self.label32)

        row3_layout = QVBoxLayout()
        row3_layout.addWidget(self.label4)
        row3_layout.addWidget(self.project_name_input)

        row3_layout.addWidget(self.label42)



        layout.addLayout(row1_layout)
        layout.addLayout(row3_layout)

        # Bouton pour sélectionner une image
        self.image_button = QPushButton("Charger une image")
        self.image_button.clicked.connect(self.load_image)
        layout.addWidget(self.image_button)

        # Label pour afficher l’aperçu de l’image
        self.image_preview = QLabel()
        self.image_preview.setFixedSize(150, 150)  # Taille de l’aperçu
        self.image_preview.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.image_preview.setStyleSheet("border: 1px solid gray;")  # Ajouter une bordure
        # Créer un layout centré pour l’image
        image_layout = QHBoxLayout()
        image_layout.addStretch()  # Ajoute un espace flexible à gauche
        image_layout.addWidget(self.image_preview, alignment=Qt.AlignmentFlag.AlignCenter)  # Ajoute l'image centrée
        image_layout.addStretch()  # Ajoute un espace flexible à droite

        layout.addLayout(image_layout)

        self.label5 = QLabel()
        layout.addWidget(self.label5)









        # Bouton d'ajout
        self.submit_button = QPushButton("Ajouter")
        self.submit_button.clicked.connect(self.post_project)  #  Ferme la fenêtre sur validation
        layout.addWidget(self.submit_button)
        self.setLayout(layout)

    def load_image(self):
        """Ouvre un QFileDialog pour sélectionner une image et vérifie si elle est carrée"""
        file_dialog = QFileDialog()
        file_dialog.setNameFilters(["Images (*.png *.jpg *.jpeg)"])
        file_dialog.setViewMode(QFileDialog.ViewMode.List)



        if file_dialog.exec():
            selected_files = file_dialog.selectedFiles()
            if selected_files:
                self.image_path = selected_files[0]  # Stocke le chemin de l’image
                file_size = os.path.getsize(self.image_path)  # Récupère la taille en octets

                max_size = 2000 * 1024  # 500 KB en octets
                pixmap = QPixmap(self.image_path)
                self.label5.setText("")

                # Vérifie si l'image est bien chargée
                if not pixmap.isNull():
                    width = pixmap.width()
                    height = pixmap.height()

                    if file_size > max_size:
                        self.label5.setText("L'image est trop lourde (max 2 MB).")
                        self.label5.setStyleSheet("color: #FF6464;")
                        self.image_preview.clear()
                        self.image_path = None
                        return

                    # Vérifie que l'image est carrée
                    if width != height:
                        self.label5.setText("L'image doit être carrée.")
                        self.label5.setStyleSheet("color: #FF6464;")
                        self.image_preview.clear()
                        self.image_path = None
                        return

                    # Vérifie que la taille est comprise entre 100x100 et 400x400 px
                    if width < 100 or width > 1000:
                        self.label5.setText("L'image doit être entre 100x100 et 1000x1000 pixels.")
                        self.label5.setStyleSheet("color: #FF6464;")
                        self.image_preview.clear()
                        self.image_path = None
                        return

                    # Si tout est valide, affiche l'image
                    scaled_pixmap = pixmap.scaled(200, 200, Qt.AspectRatioMode.KeepAspectRatio)
                    self.image_preview.setPixmap(scaled_pixmap)
                    self.label5.setText("")  # Efface le message d'erreur

                else:
                    self.label5.setText("Impossible de charger l'image.")
                    self.label5.setStyleSheet("color: #FF6464;")  # Message en rouge

    def post_project(self):
        """Vérifie les données et envoie le projet via le contrôleur."""
        project_name = self.project_name_input.text().strip()

        # Vérification via le contrôleur
        is_valid, error_message = self.controller.validate_project_name(project_name)
        if not is_valid:
            self.label42.setText(error_message)
            self.label42.setStyleSheet("color: #FF6464;")
            return

        # Envoi des données via le contrôleur
        self.image_path = self.image_path if hasattr(self, 'image_path') and self.image_path else None

        success, message = self.controller.send_project_to_xano(self.account_id, project_name, self.image_path)

        if success:
            QMessageBox.information(self, "Succès", "Projet ajouté avec succès.")
            self.controller.update_projects_options()
            self.accept()  # Ferme la boîte de dialogue
        else:
            QMessageBox.warning(self, "Erreur", f"Échec de l'ajout du projet : {message}")



class AddGroupCampaignDialog(QDialog):
    def __init__(self,account_id,account_name,project_id,project_name,controller,font_manager):
        super().__init__()

        self.controller = controller
        self.account_id = account_id
        self.account_name = account_name
        self.project_id = project_id
        self.project_name = project_name

        self.setWindowTitle("Creer un nouveau Groupe de Campagne")
        self.setFixedSize(500, 400)


        layout = QVBoxLayout()

        # Label et champ pour le nom du projet
        self.label2 = QLabel("Account :")
        self.label22 = QLabel(str(account_name))
        self.label3 = QLabel("- ID Xano:")
        self.label32 = QLabel(str(account_id))
                # Label et champ pour le nom du projet
        self.label4 = QLabel("Projet :")
        self.label42 = QLabel(str(project_name))
        self.label5 = QLabel("- Project ID Xano:")
        self.label52 = QLabel(str(project_id))

        self.label2.setFont(font_manager.get_font("bold", 14))
        self.label22.setFont(font_manager.get_font("regular", 14))
        self.label3.setFont(font_manager.get_font("bold", 14))
        self.label32.setFont(font_manager.get_font("regular", 14))
        self.label4.setFont(font_manager.get_font("bold", 14))
        self.label42.setFont(font_manager.get_font("regular", 14))
        self.label5.setFont(font_manager.get_font("bold", 14))
        self.label52.setFont(font_manager.get_font("regular", 14))


        self.label6 = QLabel("Nom du groupe de campagne :")
        self.label6.setFont(font_manager.get_font("bold", 12))
        self.label62 = QLabel()
        self.groupcampaign_name_input = QLineEdit()

        row1_layout = QHBoxLayout()
        row1_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)  #  Alignement à gauche
        row1_layout.addWidget(self.label2)
        row1_layout.addWidget(self.label22)
        row1_layout.addWidget(self.label3)
        row1_layout.addWidget(self.label32)
        row2_layout = QHBoxLayout()
        row2_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)
        row2_layout.addWidget(self.label4)
        row2_layout.addWidget(self.label42)
        row2_layout.addWidget(self.label5)
        row2_layout.addWidget(self.label52)

        row3_layout = QVBoxLayout()
        row3_layout.addWidget(self.label6)
        row3_layout.addWidget(self.groupcampaign_name_input)

        row3_layout.addWidget(self.label62)



        layout.addLayout(row1_layout)
        layout.addLayout(row2_layout)
        layout.addLayout(row3_layout)


        self.label5 = QLabel()
        layout.addWidget(self.label5)









        # Bouton d'ajout
        self.submit_button = QPushButton("Ajouter")
        self.submit_button.clicked.connect(self.post_groupcampaign)  #  Ferme la fenêtre sur validation
        layout.addWidget(self.submit_button)
        self.setLayout(layout)


    def post_groupcampaign(self):
        """Vérifie les données et envoie le projet via le contrôleur."""
        groupcampaign_name = self.groupcampaign_name_input.text().strip()

        # Vérification via le contrôleur
        is_valid, error_message = self.controller.validate_groupcampaign_name(groupcampaign_name)
        if not is_valid:
            self.label62.setText(error_message)
            self.label62.setStyleSheet("color: #FF6464;")
            return


        success, message = self.controller.send_groupcampaign_to_xano(self.project_id, groupcampaign_name)

        if success:
            msg_box = QMessageBox()
            msg_box.setWindowTitle("Succès")
            msg_box.setText("Campagne groupe ajouté avec succès.")
            # Définir une icône personnalisée
            if getattr(sys, 'frozen', False):
                base_path = sys._MEIPASS
                assets_path = os.path.join(base_path,"assets","icons")
            else:
                assets_path = os.path.abspath("assets/icons/")

            icon_path = os.path.join(assets_path, "custom_success.png")
            msg_box.setIconPixmap(QPixmap(icon_path).scaled(48, 48, Qt.AspectRatioMode.KeepAspectRatio))
            msg_box.exec()
            self.accept()  # Ferme la boîte de dialogue
        else:
            QMessageBox.warning(self, "Erreur", f"Échec de l'ajout du campagne groupe : {message}")

        self.controller.update_groupcampaigns_options()


class ModifyGroupCampaignDialog(QDialog):
    def __init__(self,account_id,account_name,project_id,project_name,controller,font_manager,groupcampaing_id,groupcampaing_name):
        super().__init__()

        self.controller = controller
        self.account_id = account_id
        self.account_name = account_name
        self.project_id = project_id
        self.project_name = project_name
        self.groupcampaing_id = groupcampaing_id
        self.groupcampaing_name = groupcampaing_name

        self.setWindowTitle("Modifier un Groupe de Campagne")
        self.setFixedSize(500, 400)


        layout = QVBoxLayout()

        # Label et champ pour le nom du projet
        self.label2 = QLabel("Account :")
        self.label22 = QLabel(str(account_name))
        self.label3 = QLabel("- ID Xano:")
        self.label32 = QLabel(str(account_id))
                # Label et champ pour le nom du projet
        self.label4 = QLabel("Projet :")
        self.label42 = QLabel(str(project_name))
        self.label5 = QLabel("- Project ID Xano:")
        self.label52 = QLabel(str(project_id))
        self.label7 = QLabel("- Group Campaign ID Xano:")
        self.label72 = QLabel(str(groupcampaing_id))

        self.label2.setFont(font_manager.get_font("bold", 14))
        self.label22.setFont(font_manager.get_font("regular", 14))
        self.label3.setFont(font_manager.get_font("bold", 14))
        self.label32.setFont(font_manager.get_font("regular", 14))
        self.label4.setFont(font_manager.get_font("bold", 14))
        self.label42.setFont(font_manager.get_font("regular", 14))
        self.label5.setFont(font_manager.get_font("bold", 14))
        self.label52.setFont(font_manager.get_font("regular", 14))
        self.label7.setFont(font_manager.get_font("bold", 14))
        self.label72.setFont(font_manager.get_font("regular", 14))


        self.label6 = QLabel("Nom du groupe de campagne :")
        self.label6.setFont(font_manager.get_font("bold", 12))
        self.label62 = QLabel()
        self.groupcampaign_name_input = QLineEdit()
        self.groupcampaign_name_input.setText(groupcampaing_name)

        row1_layout = QHBoxLayout()
        row1_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)  #  Alignement à gauche
        row1_layout.addWidget(self.label2)
        row1_layout.addWidget(self.label22)
        row1_layout.addWidget(self.label3)
        row1_layout.addWidget(self.label32)
        row2_layout = QHBoxLayout()
        row2_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)
        row2_layout.addWidget(self.label4)
        row2_layout.addWidget(self.label42)
        row2_layout.addWidget(self.label5)
        row2_layout.addWidget(self.label52)
        row4_layout = QHBoxLayout()
        row4_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)

        row4_layout.addWidget(self.label7)
        row4_layout.addWidget(self.label72)


        row3_layout = QVBoxLayout()
        row3_layout.addWidget(self.label6)
        row3_layout.addWidget(self.groupcampaign_name_input)

        row3_layout.addWidget(self.label62)



        layout.addLayout(row1_layout)
        layout.addLayout(row2_layout)
        layout.addLayout(row4_layout)
        layout.addLayout(row3_layout)


        self.label5 = QLabel()
        layout.addWidget(self.label5)









        # Bouton d'ajout
        self.submit_button = QPushButton("Modifier")
        self.submit_button.clicked.connect(self.modify_groupcampaign)  #  Ferme la fenêtre sur validation
        layout.addWidget(self.submit_button)
        self.setLayout(layout)


    def modify_groupcampaign(self):
        """Vérifie les données et envoie le projet via le contrôleur."""
        groupcampaign_name = self.groupcampaign_name_input.text().strip()

        # Vérification via le contrôleur
        is_valid, error_message = self.controller.validate_groupcampaign_name(groupcampaign_name)
        if not is_valid:
            self.label62.setText(error_message)
            self.label62.setStyleSheet("color: #FF6464;")
            return


        success, message = self.controller.modify_groupcampaign_to_xano(self.project_id, groupcampaign_name,self.groupcampaing_id)

        if success:
            msg_box = QMessageBox()
            msg_box.setWindowTitle("Succès")
            msg_box.setText("Campagne groupe modifié avec succès.")
            # Définir une icône personnalisée
            if getattr(sys, 'frozen', False):
                base_path = sys._MEIPASS
                assets_path = os.path.join(base_path,"assets","icons")
            else:
                assets_path = os.path.abspath("assets/icons/")

            icon_path = os.path.join(assets_path, "custom_success.png")
            msg_box.setIconPixmap(QPixmap(icon_path).scaled(48, 48, Qt.AspectRatioMode.KeepAspectRatio))
            msg_box.exec()
            self.accept()  # Ferme la boîte de dialogue
        else:
            QMessageBox.warning(self, "Erreur", f"Échec de modification du campagne groupe : {message}")

        self.controller.update_groupcampaigns_options()




class AddCampaignDialog(QDialog):
    def __init__(self, account_id, account_name, project_id, project_name, groupcampaign_id, groupcampaign_name, controller, font_manager, campaign_id=None,read_only=False):
        super().__init__()

        self._init_metadata(
            account_id, account_name,
            project_id, project_name,
            groupcampaign_id, groupcampaign_name,
            controller
        )
        self.read_only = read_only

        if campaign_id is not None:
            self.campaign_id_xano = campaign_id
            self.campaign_data = self.controller.update_campaign_dialog(campaign_id)
        else:
            self.campaign_data = None



        self.setWindowTitle("Modifier une campagne" if self.campaign_data else "Créer une nouvelle campagne")
        self.setFixedSize(900, 900)

        layout = QVBoxLayout()
        layout.setAlignment(Qt.AlignmentFlag.AlignTop)


        layout.addLayout(self._build_header_info(font_manager))
        layout.addSpacing(40)
        layout.addWidget(self._build_title_section(font_manager))

        layout.addSpacing(40)
        layout.addLayout(self._build_form_layout(font_manager))


        # Bouton d'ajout
        self.submit_button = QPushButton("Modifier" if self.campaign_data else "Créer la campagne")
        if self.campaign_data:
            self.submit_button.clicked.connect(self.modify_campaign)
        else:
            self.submit_button.clicked.connect(self.post_campaign)

        layout.addWidget(self.submit_button)


        if self.campaign_data:
            self.populate_fields_from_campaign_data(self.campaign_data[0])
            if self.read_only:
                self.submit_button.setVisible(False)
                self.campaign_name_input.setEnabled(False)
                self.levier_dropdown.setEnabled(False)
                self.objectif_dropdown.setEnabled(False)
                self.budget_marge_input.setEnabled(False)
                self.budget_non_marge_input.setEnabled(False)
                self.state_dropdown.setEnabled(False)
                self.start_date_input.setEnabled(False)
                self.end_date_input.setEnabled(False)
                self.admin_account_dropdown.setEnabled(False)
                self.ad_account_dropdown.setEnabled(False)
                self.campaign_id_input.setEnabled(False)

        self.setLayout(layout)


    def import_campaign_params(self):
        """Importe les paramètres de la campagne à partir d'un texte copié-collé."""
        # Créer une boîte de dialogue pour entrer le texte
        dialog = QDialog(self)
        dialog.setWindowTitle("Importer des paramètres")
        dialog.setFixedSize(600, 300)

        layout = QVBoxLayout()

        # Instructions
        instructions = QLabel("Collez le texte contenant les paramètres de la campagne ci-dessous.\nFormat attendu: ID_CAMPAGNE\tNOM DE CAMPAGNE\tÉTAT\tLEVIER\tOBJECTIF\tDATES (DÉBUT)\tDATES (FIN)\tBUDGET MARGÉ\tBUDGET NON-MARGÉ")
        instructions.setWordWrap(True)
        layout.addWidget(instructions)

        # Champ de texte
        text_input = QLineEdit()
        text_input.setPlaceholderText("Ex: 120223141727060206\tAvr25#09 Paris Society - La Clairière - Prog 2025 - vague 1 - b400 j10\tActive\tMetaAds\tVente\t18/04/2025\t27/04/2025\t400,00 €\t280,00 €")
        layout.addWidget(text_input)

        # Boutons
        button_layout = QHBoxLayout()
        import_button = QPushButton("Importer")
        cancel_button = QPushButton("Annuler")
        button_layout.addWidget(import_button)
        button_layout.addWidget(cancel_button)
        layout.addLayout(button_layout)

        dialog.setLayout(layout)

        # Connecter les boutons
        import_button.clicked.connect(lambda: self._process_import_text(text_input.text(), dialog))
        cancel_button.clicked.connect(dialog.reject)

        dialog.exec()

    def _process_import_text(self, text, dialog):
        """Traite le texte importé et remplit les champs du formulaire."""
        if not text.strip():
            self.import_error_label.setText("Erreur: Texte vide")
            return

        # Diviser le texte en colonnes (séparées par des tabulations ou plusieurs espaces)
        columns = re.split(r'\t|\s{2,}', text.strip())

        if len(columns) < 9:
            self.import_error_label.setText(f"Erreur: Format incorrect. {len(columns)} colonnes trouvées, 9 attendues.")
            return

        try:
            # Extraire les valeurs
            campaign_id = columns[0].strip()
            campaign_name = columns[1].strip()
            state = columns[2].strip()
            levier = columns[3].strip()
            objectif = columns[4].strip()
            start_date_str = columns[5].strip()
            end_date_str = columns[6].strip()
            budget_marge_str = columns[7].strip().replace('€', '').replace(',', '.').strip()
            budget_non_marge_str = columns[8].strip().replace('€', '').replace(',', '.').strip()

            # Vérifier que le levier existe
            if levier not in LEVIER_OBJECTIFS_MAP:
                self.import_error_label.setText(f"Erreur: Levier '{levier}' non reconnu. Options valides: {', '.join(LEVIER_OBJECTIFS_MAP.keys())}")
                return

            # Vérifier que l'objectif est valide pour ce levier
            if objectif not in LEVIER_OBJECTIFS_MAP.get(levier, []):
                self.import_error_label.setText(f"Erreur: Objectif '{objectif}' non valide pour le levier '{levier}'")
                return

            # Vérifier que l'état est valide
            valid_states = ["Waiting", "Audit", "Active", "Paused", "Ended", "Rejected"]
            if state not in valid_states:
                self.import_error_label.setText(f"Erreur: État '{state}' non reconnu. Options valides: {', '.join(valid_states)}")
                return

            # Convertir les dates (format DD/MM/YYYY)
            try:
                # Convertir la date de début
                if '/' in start_date_str:
                    day, month, year = start_date_str.split('/')
                    start_date = QDate(int(year), int(month), int(day))
                else:
                    self.import_error_label.setText(f"Erreur: Format de date de début incorrect '{start_date_str}'. Format attendu: DD/MM/YYYY")
                    return

                # Convertir la date de fin
                if '/' in end_date_str:
                    day, month, year = end_date_str.split('/')
                    end_date = QDate(int(year), int(month), int(day))
                else:
                    self.import_error_label.setText(f"Erreur: Format de date de fin incorrect '{end_date_str}'. Format attendu: DD/MM/YYYY")
                    return
            except Exception as e:
                self.import_error_label.setText(f"Erreur lors de la conversion des dates: {str(e)}")
                return

            # Convertir les budgets (accepter les formats à virgule)
            try:
                # Convertir en float d'abord pour gérer les formats comme "200,50"
                budget_marge_float = float(budget_marge_str)
                budget_non_marge_float = float(budget_non_marge_str)

                # Convertir en entiers pour l'affichage
                budget_marge = int(budget_marge_float)
                budget_non_marge = int(budget_non_marge_float)
            except ValueError:
                self.import_error_label.setText("Erreur: Les budgets doivent être des nombres valides")
                return

            # Remplir les champs du formulaire
            self.campaign_name_input.setText(campaign_name)
            self.levier_dropdown.setCurrentText(levier)
            self.update_objectif_options()  # Mettre à jour les options d'objectif
            self.objectif_dropdown.setCurrentText(objectif)
            self.state_dropdown.setCurrentText(state)
            self.start_date_input.setDate(start_date)
            self.end_date_input.setDate(end_date)
            self.budget_marge_input.setText(str(budget_marge))
            self.budget_non_marge_input.setText(str(budget_non_marge))
            self.campaign_id_input.setText(campaign_id)  # Remplir le champ ID de campagne

            # Effacer le message d'erreur et fermer la boîte de dialogue
            self.import_error_label.setText("")
            dialog.accept()

        except Exception as e:
            self.import_error_label.setText(f"Erreur lors de l'importation: {str(e)}")

    def populate_fields_from_campaign_data(self, campaign_data):
        self.campaign_name_input.setText(campaign_data.get("campaign_name", ""))
        levier = campaign_data.get("leveler", "")
        if levier in LEVIER_OBJECTIFS_MAP:
            self.levier_dropdown.setCurrentText(levier)
            self.update_objectif_options()
            objectif = campaign_data.get("objective", "")
            self.objectif_dropdown.setCurrentText(objectif)
        self.budget_marge_input.setText(str(campaign_data.get("total_budget", "")))
        self.budget_non_marge_input.setText(str(campaign_data.get("nomarginbudget", "")))
        self.state_dropdown.setCurrentText(campaign_data.get("state", "Active"))
        try:
            self.start_date_input.setDate(QDate.fromString(campaign_data.get("start_date", ""), "yyyy-MM-dd"))
            self.end_date_input.setDate(QDate.fromString(campaign_data.get("end_date", ""), "yyyy-MM-dd"))
        except:
            pass
        self.campaign_id_input.setText(campaign_data.get("ID_Campaign", ""))

        # Préselectionner Admin et Ad Account si présents dans les données
        for i in range(self.admin_account_dropdown.count()):
            item = self.admin_account_dropdown.itemData(i)
            if item and item.get("id_account") == campaign_data.get("ID_adminadaccount"):
                self.admin_account_dropdown.setCurrentIndex(i)
                break
        self.update_ad_account_options()
        for i in range(self.ad_account_dropdown.count()):
            item = self.ad_account_dropdown.itemData(i)
            if item and item.get("id_account") == campaign_data.get("ID_AdAccount"):
                self.ad_account_dropdown.setCurrentIndex(i)
                break

    def _init_metadata(self, account_id, account_name, project_id, project_name, groupcampaign_id, groupcampaign_name, controller):
        self.controller = controller
        self.account_id = account_id
        self.account_name = account_name
        self.project_id = project_id
        self.project_name = project_name
        self.groupcampaign_id = groupcampaign_id
        self.groupcampaign_name = groupcampaign_name

    def _build_header_info(self, font_manager):
        layout = QVBoxLayout()

        self.label2 = QLabel("Account :")
        self.label22 = QLabel(str(self.account_name))
        self.label3 = QLabel("- ID Xano:")
        self.label32 = QLabel(str(self.account_id))
        self.label4 = QLabel("Projet :")
        self.label42 = QLabel(str(self.project_name))
        self.label5 = QLabel("- Project ID Xano:")
        self.label52 = QLabel(str(self.project_id))
        self.label6 = QLabel("Groupe de campagne :")
        self.label62 = QLabel(str(self.groupcampaign_name))
        self.label7 = QLabel("- Groupe de campagne ID Xano:")
        self.label72 = QLabel(str(self.groupcampaign_id))

        self.label2.setFont(font_manager.get_font("bold", 14))
        self.label22.setFont(font_manager.get_font("regular", 14))
        self.label3.setFont(font_manager.get_font("bold", 14))
        self.label32.setFont(font_manager.get_font("regular", 14))
        self.label4.setFont(font_manager.get_font("bold", 14))
        self.label42.setFont(font_manager.get_font("regular", 14))
        self.label5.setFont(font_manager.get_font("bold", 14))
        self.label52.setFont(font_manager.get_font("regular", 14))

        # Row 1 layout (centered)
        row1_layout = QHBoxLayout()
        row1_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)
        row1_layout.addWidget(self.label2)
        row1_layout.addWidget(self.label22)
        row1_layout.addWidget(self.label3)
        row1_layout.addWidget(self.label32)
        row1_layout.addWidget(self.label4)
        row1_layout.addWidget(self.label42)
        row1_layout.addWidget(self.label5)
        row1_layout.addWidget(self.label52)

        # Row 2 layout (aligned left)
        row2_layout = QHBoxLayout()
        row2_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)
        row2_layout.addWidget(self.label6)
        row2_layout.addWidget(self.label62)
        row2_layout.addWidget(self.label7)
        row2_layout.addWidget(self.label72)

        layout.addLayout(row1_layout)
        layout.addSpacing(10)
        layout.addLayout(row2_layout)

        return layout

    def _build_title_section(self, font_manager):
        title_layout = QVBoxLayout()

        title_label = QLabel("Modifier une campagne" if self.campaign_data else "Créer une nouvelle campagne")
        title_label.setFont(font_manager.get_font("bold", 20))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_layout.addWidget(title_label)

        # Ajouter un bouton d'importation de paramètres
        import_layout = QHBoxLayout()
        import_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.import_button = QPushButton("Importer des paramètres")
        self.import_button.setFixedWidth(200)
        self.import_button.clicked.connect(self.import_campaign_params)
        import_layout.addWidget(self.import_button)

        title_layout.addLayout(import_layout)

        # Ajouter un label pour les messages d'erreur d'importation
        self.import_error_label = QLabel("")
        self.import_error_label.setStyleSheet("color: red;")
        self.import_error_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_layout.addWidget(self.import_error_label)

        container = QWidget()
        container.setLayout(title_layout)
        return container

    def _build_form_layout(self, font_manager):
        form_layout = QFormLayout()
        # Formulaire pour les champs de la campagne
        form_layout = QFormLayout()
        form_layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.ExpandingFieldsGrow)  # Permet aux champs de grandir
        form_layout.setRowWrapPolicy(QFormLayout.RowWrapPolicy.DontWrapRows)  # Empêche les lignes de se replier
        form_layout.setVerticalSpacing(20)  # Ajoute un espacement vertical entre les champs

        # Nom de la campagne
        self.campaign_name_input = QLineEdit()
        self.campaign_name_input.setFixedWidth(600)  # Augmente la taille de l'input
        form_layout.addRow("Nom de la campagne :", self.campaign_name_input)


        spacer = QWidget()
        spacer.setFixedHeight(20)
        form_layout.addRow(spacer)

        #######   LEVIER   ########


        # Ajouter un titre pour Levier et Objectif
        form_layout.addRow(self._create_section_title("Levier et Objectif :", font_manager))
        # Levier (enum)
        self.levier_dropdown = QComboBox()
        self.levier_dropdown.addItems(LEVIER_OBJECTIFS_MAP.keys())

        # Objectif (enum dépendant du levier)
        self.objectif_dropdown = QComboBox()
        self.update_objectif_options()

        # Layout horizontal pour Levier et Objectif
        levier_objectif_layout = QHBoxLayout()
        levier_objectif_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)

        # Levier
        levier_layout = QHBoxLayout()
        label_levier = QLabel("Levier :")
        label_levier.setFixedWidth(60)
        levier_layout.addWidget(label_levier)
        levier_layout.addWidget(self.levier_dropdown)

        # Objectif
        objectif_layout = QHBoxLayout()
        label_objectif = QLabel("Objectif :")
        label_objectif.setFixedWidth(70)
        objectif_layout.addWidget(label_objectif)
        objectif_layout.addWidget(self.objectif_dropdown)

        # Ajouter les deux au layout principal
        levier_objectif_layout.addLayout(levier_layout)
        levier_objectif_layout.addSpacing(20)
        levier_objectif_layout.addLayout(objectif_layout)

        form_layout.addRow(levier_objectif_layout)

        # Connecter levier pour mettre à jour les objectifs
        self.levier_dropdown.currentIndexChanged.connect(self.update_objectif_options)



        spacer = QWidget()
        spacer.setFixedHeight(5)
        form_layout.addRow(spacer)

        #######   BUDGET   ########



        form_layout.addRow(self._create_section_title("Budgets :", font_manager))

        # Créer un layout horizontal pour aligner les deux paires (label + champ)
        budget_layout = QHBoxLayout()
        budget_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)

        # Layout pour Budget margé
        budget_marge_layout = QHBoxLayout()
        label_budget_marge = QLabel("Budget margé :")
        label_budget_marge.setFixedWidth(100)
        self.budget_marge_input = QLineEdit()
        self.budget_marge_input.setFixedWidth(100)
        self.budget_marge_input.setValidator(QDoubleValidator(0.0, 9999999.99, 2))  # Contraint à des nombres décimaux
        budget_marge_layout.addWidget(label_budget_marge)
        budget_marge_layout.addWidget(self.budget_marge_input)

        # Layout pour Budget non margé
        budget_non_marge_layout = QHBoxLayout()
        label_budget_non_marge = QLabel("Budget non margé :")
        label_budget_non_marge.setFixedWidth(120)
        self.budget_non_marge_input = QLineEdit()
        self.budget_non_marge_input.setFixedWidth(100)
        self.budget_non_marge_input.setValidator(QDoubleValidator(0, 9999999, 0))  # Contraint à des nombres entiers
        budget_non_marge_layout.addWidget(label_budget_non_marge)
        budget_non_marge_layout.addWidget(self.budget_non_marge_input)

        # Layout pour la marge calculée
        margin_layout = QHBoxLayout()
        label_margin = QLabel("Marge :")
        label_margin.setFixedWidth(50)
        self.margin_value_label = QLabel("0 %")  # Label pour afficher la marge calculée
        self.margin_value_label.setFixedWidth(100)
        margin_layout.addWidget(label_margin)
        margin_layout.addWidget(self.margin_value_label)

        # Connecter les champs de budget pour recalculer la marge
        self.budget_marge_input.textChanged.connect(self.calculate_margin)
        self.budget_non_marge_input.textChanged.connect(self.calculate_margin)

        # Ajouter les layouts au formulaire
        budget_layout.addLayout(budget_marge_layout)
        budget_layout.addSpacing(20)  # Espacement entre les deux groupes
        budget_layout.addLayout(budget_non_marge_layout)
        budget_layout.addSpacing(20)  # Espacement entre les budgets et la marge
        budget_layout.addLayout(margin_layout)





        # Ajouter cette ligne complète au formulaire
        form_layout.addRow(budget_layout)

        #######   ETATS   ########

        spacer = QWidget()
        spacer.setFixedHeight(5)
        form_layout.addRow(spacer)

        form_layout.addRow(self._create_section_title("Attributs :", font_manager))


        # State (enum), Start date, End date
        self.state_dropdown = QComboBox()
        self.state_dropdown.addItems(["Waiting","Audit","Active","Paused", "Ended","Rejected"])
        self.start_date_input = QDateEdit()
        self.start_date_input.setFixedSize(150, 30)  # Augmente la taille de la box
        self.start_date_input.setStyleSheet(self._get_dateedit_style())

        self.start_date_input.setCalendarPopup(True)
        self.start_date_input.setDisplayFormat("yyyy-MM-dd")
        self.start_date_input.setDate(QDate.currentDate())
        self.end_date_input = QDateEdit()
        self.end_date_input.setFixedSize(150, 30)  # Augmente la taille de la box
        self.end_date_input.setStyleSheet(self._get_dateedit_style())

        self.end_date_input.setCalendarPopup(True)
        self.end_date_input.setDisplayFormat("yyyy-MM-dd")
        self.end_date_input.setDate(QDate.currentDate())

        # Layout for State, Start date, and End date
        state_date_layout = QHBoxLayout()
        state_date_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)

        # Label for date validation error
        self.date_error_label = QLabel("")
        self.date_error_label.setStyleSheet("color: red;")
        self.date_error_label.setVisible(True)

        # Connect date inputs to validation
        self.start_date_input.dateChanged.connect(self.validate_dates)
        self.end_date_input.dateChanged.connect(self.validate_dates)


        # Add the error label below the date inputs




        # State
        state_layout = QHBoxLayout()
        label_state = QLabel("État :")
        label_state.setFixedWidth(50)
        state_layout.addWidget(label_state)
        state_layout.addWidget(self.state_dropdown)

        # Start date
        start_date_layout = QHBoxLayout()
        label_start_date = QLabel("Date de début :")
        label_start_date.setFixedWidth(100)
        start_date_layout.addWidget(label_start_date)
        start_date_layout.addWidget(self.start_date_input)

        # End date
        end_date_layout = QHBoxLayout()
        label_end_date = QLabel("Date de fin :")
        label_end_date.setFixedWidth(80)
        end_date_layout.addWidget(label_end_date)
        end_date_layout.addWidget(self.end_date_input)

        # Add all to the main layout
        state_date_layout.addLayout(state_layout)
        state_date_layout.addSpacing(20)
        state_date_layout.addLayout(start_date_layout)
        state_date_layout.addSpacing(20)
        state_date_layout.addLayout(end_date_layout)

        form_layout.addRow(state_date_layout)
        form_layout.addRow(self.date_error_label)


        #######   BUSINESS MANAGER   ########
        self.admin_accounts_data, self.ad_accounts_data = self.controller.update_bmaccounts()
        form_layout.addRow(self._create_section_title("Business Manager settings :", font_manager))


        # ID Admin Account (enum)
        self.admin_account_dropdown = QComboBox()
        self.admin_account_dropdown.addItem("Sélectionner un admin...")  # par défaut
        for admin in self.admin_accounts_data:
            name = admin["name"] or "Sans nom"
            leveler = admin["leveler"] or "Sans levier"
            self.admin_account_dropdown.addItem(f"{name} - {leveler}", admin)

        self.admin_account_id_label = QLabel("")  # Label vide pour afficher l'ID
        admin_account_layout = QHBoxLayout()
        admin_account_layout.addWidget(self.admin_account_dropdown)
        admin_account_layout.addWidget(self.admin_account_id_label)
        form_layout.addRow("Admin Account :", admin_account_layout)

        # ID Ad Account (enum dépendant de l'admin account)
        self.ad_account_dropdown = QComboBox()
        self.ad_account_dropdown.setFixedWidth(300)
        self.ad_account_id_label = QLabel("")  # Label vide pour afficher l'ID
        ad_account_layout = QHBoxLayout()
        ad_account_layout.addWidget(self.ad_account_dropdown)
        ad_account_layout.addWidget(self.ad_account_id_label)
        form_layout.addRow("Ad Account :", ad_account_layout)

        # Connecter admin account pour mettre à jour les ad accounts et afficher l'ID
        self.admin_account_dropdown.currentIndexChanged.connect(self.update_ad_account_options)
        self.ad_account_dropdown.currentIndexChanged.connect(self.update_ad_account_id_label)

        # ID Campagne (texte)
        self.campaign_id_input = QLineEdit()
        form_layout.addRow("ID Campagne :", self.campaign_id_input)


        return form_layout



    def _get_dateedit_style(self):
        return """
            QDateEdit {
            outline: none;
            border: 1px solid gray;
            border-radius: 5px;
            padding: 2px;
            }
            QDateEdit:focus {
            border: 1px solid #888;
            }

            QCalendarWidget QWidget {
            background-color: white;
            color: black;

            }

            QCalendarWidget QToolButton {
            background-color: none;
            color: black;
            font-weight: bold;
            border: none;
            margin: 5px;
            }

            QCalendarWidget QToolButton:hover {
            background-color: #eeeeee;
            }

            QCalendarWidget QSpinBox {
            width: 70px;
            font-size: 12px;
            color: black;
            background-color: none;
            border: none;
            }

            QCalendarWidget QMenu {
            background-color: white;
            border: 1px solid gray;
            }

            QCalendarWidget QAbstractItemView {
            selection-background-color: #476AFF;
            selection-color: white;
            background-color: #fefefe;
            gridline-color: lightgray;
            }

            QCalendarWidget QHeaderView::section {
            background-color: #476AFF;
            color: white;
            font-weight: bold;
            }

            QCalendarWidget QAbstractItemView::item {
            color: black;
            }

            QCalendarWidget QAbstractItemView::item:enabled:nth-child(7),  /* Saturday */
            QCalendarWidget QAbstractItemView::item:enabled:nth-child(1)   /* Sunday */
            {
            color: #476AFF;
            }
            QCalendarWidget QToolButton#qt_calendar_prevmonth {
            qproperty-icon: url("assets/icons/arrow-circle-left.png");
            qproperty-iconSize: 20px 20px;
            }

            QCalendarWidget QToolButton#qt_calendar_nextmonth {
            qproperty-icon: url("assets/icons/arrow-circle-right.png");
            qproperty-iconSize: 20px 20px;
            }


        """

    def _create_section_title(self, text, font_manager):
        title = QLabel(text)
        title.setFont(font_manager.get_font("bold", 15))
        title.setStyleSheet("color: #476AFF;")
        return title

    def calculate_margin(self):
        """Calcule la marge entre le budget margé et non margé."""
        try:
            budget_marge = float(self.budget_marge_input.text()) if self.budget_marge_input.text() else 0.0
            budget_non_marge = float(self.budget_non_marge_input.text()) if self.budget_non_marge_input.text() else 0.0
            if budget_marge == 0:
                self.margin_value_label.setText("∞ %")
                return
            margin = 1 - (budget_non_marge / budget_marge)
            margin_percentage = margin * 100  # Convertir en pourcentage
            self.margin_value_label.setText(f"{margin_percentage:.0f}%")
        except ValueError:
            self.margin_value_label.setText("Erreur")


    def validate_dates(self):
        """Validate that the start date is before or equal to the end date."""
        start_date = self.start_date_input.date()
        end_date = self.end_date_input.date()

        if start_date > end_date:
            self.date_error_label.setText("La date de début doit être antérieure ou égale à la date de fin.")
        else:
            self.date_error_label.setText("")



    def update_objectif_options(self):
        levier = self.levier_dropdown.currentText()
        self.objectif_dropdown.clear()
        objectifs = LEVIER_OBJECTIFS_MAP.get(levier, [])
        self.objectif_dropdown.addItems(objectifs)

    def update_ad_account_options(self):
        """Met à jour les ad accounts selon le levier de l’admin sélectionné."""
        self.ad_account_dropdown.clear()
        selected_admin = self.admin_account_dropdown.currentData()
        if not selected_admin:
            self.admin_account_id_label.setText("ID:")
            return

        self.admin_account_id_label.setText(f"ID: {selected_admin.get('id_account') or 'N/A'}")

        levier = selected_admin.get("leveler")
        matching_ad_accounts = [ad for ad in self.ad_accounts_data if ad.get("leveler") == levier]

        for ad in matching_ad_accounts:
            label = ad["name"] or "Sans nom"
            self.ad_account_dropdown.addItem(label, ad)

        self.update_ad_account_id_label()

    def update_ad_account_id_label(self):
        selected_ad = self.ad_account_dropdown.currentData()
        self.ad_account_id_label.setText(f"ID: {selected_ad.get('id_account') or 'N/A'}" if selected_ad else "ID:")


    def post_campaign(self):
        """Récupère les données du formulaire et les envoie au contrôleur."""
        groupcampaign_id = self.groupcampaign_id
        campaign_name = self.campaign_name_input.text().strip()
        levier = self.levier_dropdown.currentText()
        objectif = self.objectif_dropdown.currentText()
        state = self.state_dropdown.currentText()
        start_date = self.start_date_input.date().toString("yyyy-MM-dd")
        end_date = self.end_date_input.date().toString("yyyy-MM-dd")
        admin_account = self.admin_account_dropdown.currentData()
        ad_account = self.ad_account_dropdown.currentData()
        campaign_id = self.campaign_id_input.text().strip()







        try:
            # Convertir en float d'abord puis en int
            budget_marge = float(self.budget_marge_input.text())
        except ValueError:
            budget_marge = 0

        try:
            # Convertir en float d'abord puis en int
            budget_non_marge = float(self.budget_non_marge_input.text())
        except ValueError:
            budget_non_marge = 0

        admin_id = admin_account.get("id_account") if admin_account else None
        admin_name = admin_account.get("name", "N/A") if admin_account else "N/A"
        ad_id = ad_account.get("id_account") if ad_account else None
        ad_name = ad_account.get("name", "N/A") if ad_account else "N/A"


        data = {
            "campaigngroup_id": groupcampaign_id,
            "campaign_name": campaign_name,
            "leveler": levier,
            "objective": objectif,
            "total_budget": budget_marge,
            "nomarginbudget": budget_non_marge,
            "state": state,
            "start_date": start_date,
            "end_date": end_date,
            "ID_adminadaccount": admin_id,
            "ID_AdAccount": ad_id,
            "ID_Campaign": campaign_id
        }

        confirmation_text = f"""
        Confirmez-vous l'envoi de cette campagne ?

        Groupe campagne ID : {groupcampaign_id}

        Nom campagne : {campaign_name}
        Levier : {levier}
        Objectif : {objectif}

        Budget margé : {budget_marge} - Budget non margé : {budget_non_marge}

        État : {state}
        Date début : {start_date} - Date fin : {end_date}

        Admin Account : {admin_name} (ID : {admin_id})
        Ad Account : {ad_name} (ID : {ad_id})


        Campagne ID : {campaign_id}
        """
        confirmation = QMessageBox.question(
            self,
            "Confirmation d'envoi",
            confirmation_text,
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if confirmation != QMessageBox.StandardButton.Yes:
            return



        success, message = self.controller.send_campaign_to_xano(data)

        if success:
            msg_box = QMessageBox()
            msg_box.setWindowTitle("Succès")
            msg_box.setText("Campagne ajouté avec succès.")
            # Définir une icône personnalisée
            if getattr(sys, 'frozen', False):
                base_path = sys._MEIPASS
                assets_path = os.path.join(base_path,"assets","icons")
            else:
                assets_path = os.path.abspath("assets/icons/")

            icon_path = os.path.join(assets_path, "custom_success.png")
            msg_box.setIconPixmap(QPixmap(icon_path).scaled(48, 48, Qt.AspectRatioMode.KeepAspectRatio))
            msg_box.exec()
            self.accept()  # Ferme la boîte de dialogue
        else:
            QMessageBox.warning(self, "Erreur", f"Échec de l'ajout de la campagne : {message}")

        self.controller.update_campaigns_options()


    def modify_campaign(self):
        """Récupère les données du formulaire et les envoie au contrôleur."""
        groupcampaign_id = self.groupcampaign_id
        campaign_name = self.campaign_name_input.text().strip()
        levier = self.levier_dropdown.currentText()
        objectif = self.objectif_dropdown.currentText()
        state = self.state_dropdown.currentText()
        start_date = self.start_date_input.date().toString("yyyy-MM-dd")
        end_date = self.end_date_input.date().toString("yyyy-MM-dd")
        admin_account = self.admin_account_dropdown.currentData()
        ad_account = self.ad_account_dropdown.currentData()
        campaign_id = self.campaign_id_input.text().strip()







        try:

            budget_marge = float(self.budget_marge_input.text())
        except ValueError:
            budget_marge = 0

        try:

            budget_non_marge = float(self.budget_non_marge_input.text())
        except ValueError:
            budget_non_marge = 0

        admin_id = admin_account.get("id_account") if admin_account else None
        admin_name = admin_account.get("name", "N/A") if admin_account else "N/A"
        ad_id = ad_account.get("id_account") if ad_account else None
        ad_name = ad_account.get("name", "N/A") if ad_account else "N/A"


        data = {
            "campaign_id": self.campaign_id_xano,
            "campaigngroup_id": groupcampaign_id,
            "campaign_name": campaign_name,
            "leveler": levier,
            "objective": objectif,
            "total_budget": budget_marge,
            "nomarginbudget": budget_non_marge,
            "state": state,
            "start_date": start_date,
            "end_date": end_date,
            "ID_adminadaccount": admin_id,
            "ID_AdAccount": ad_id,
            "ID_Campaign": campaign_id
        }

        confirmation_text = f"""
        Confirmez-vous l'envoi de cette campagne ?

        Groupe campagne ID : {groupcampaign_id}

        Nom campagne : {campaign_name}
        Levier : {levier}
        Objectif : {objectif}

        Budget margé : {budget_marge} - Budget non margé : {budget_non_marge}

        État : {state}
        Date début : {start_date} - Date fin : {end_date}

        Admin Account : {admin_name} (ID : {admin_id})
        Ad Account : {ad_name} (ID : {ad_id})


        Campagne ID : {campaign_id}
        """
        confirmation = QMessageBox.question(
            self,
            "Confirmation d'envoi",
            confirmation_text,
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if confirmation != QMessageBox.StandardButton.Yes:
            return



        success, message = self.controller.modify_campaign_to_xano(data)

        if success:
            msg_box = QMessageBox()
            msg_box.setWindowTitle("Succès")
            msg_box.setText("Campagne modifié avec succès.")
            # Définir une icône personnalisée
            if getattr(sys, 'frozen', False):
                base_path = sys._MEIPASS
                assets_path = os.path.join(base_path,"assets","icons")
            else:
                assets_path = os.path.abspath("assets/icons/")

            icon_path = os.path.join(assets_path, "custom_success.png")
            msg_box.setIconPixmap(QPixmap(icon_path).scaled(48, 48, Qt.AspectRatioMode.KeepAspectRatio))
            msg_box.exec()
            self.accept()  # Ferme la boîte de dialogue
        else:
            QMessageBox.warning(self, "Erreur", f"Échec de la modification de la campagne : {message}")

        self.controller.update_campaigns_options()


def apply_rounded_mask(label, radius=10):
    """Applique un masque arrondi à un QLabel contenant une image."""
    size = label.size()
    mask = QPixmap(size)
    mask.fill(Qt.GlobalColor.transparent)

    painter = QPainter(mask)
    painter.setRenderHint(QPainter.RenderHint.Antialiasing)
    painter.setBrush(QBrush(Qt.GlobalColor.white))
    painter.setPen(Qt.PenStyle.NoPen)
    painter.drawRoundedRect(QRect(0, 0, size.width(), size.height()), radius, radius)
    painter.end()

    label.setMask(mask.createMaskFromColor(Qt.GlobalColor.transparent))
