import sys
import traceback
from PyQt6.QtWidgets import (QApplication, QLineEdit, QInputDialog, QMessageBox,
                             QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
                             QGridLayout, QMenuBar)
from PyQt6.QtGui import QIcon, QKeySequence, QAction, QPixmap
from PyQt6.QtCore import Qt
# Les importations de MainWindow sont faites localement pour éviter les imports circulaires

from tools.font_manager import FontManager
import os


class ToolSelectionWindow(QMainWindow):
    def __init__(self, font_manager):
        super().__init__()
        self.font_manager = font_manager
        self.setWindowTitle("SA Corp - Sélection d'outils")
        self.setGeometry(100, 100, 600, 400)
        self.setFont(self.font_manager.get_font("regular", 12))

        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Layout principal
        main_layout = QVBoxLayout(central_widget)

        # Logo en haut de la fenêtre
         
        if getattr(sys, 'frozen', False):
            base_path = sys._MEIPASS
            logo_pixmap = QPixmap(os.path.join(base_path, "assets", "logotool.png"))
        else:
            logo_pixmap = QPixmap("assets/logotool.png")




        logo_label = QLabel()
        # logo_pixmap = QPixmap("logotool.png")
        if not logo_pixmap.isNull():
            # Redimensionner l'image pour qu'elle ait une largeur de 300 pixels tout en conservant le ratio
            logo_pixmap = logo_pixmap.scaledToWidth(300, Qt.TransformationMode.SmoothTransformation)
            logo_label.setPixmap(logo_pixmap)
            logo_label.setAlignment(Qt.AlignmentFlag.AlignLeft)

            # Créer un layout horizontal pour le logo
            logo_layout = QHBoxLayout()
            logo_layout.addWidget(logo_label)
            logo_layout.addStretch()  # Ajoute un espace extensible à droite du logo

            main_layout.addLayout(logo_layout)
            main_layout.addSpacing(20)  # Espace entre le logo et le titre

        # Titre
        title_label = QLabel("Sélectionnez un outil à lancer")
        title_label.setFont(self.font_manager.get_font("bold", 18))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        main_layout.addWidget(title_label)

        # Layout vertical pour les boutons
        button_layout = QVBoxLayout()
        button_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        main_layout.addLayout(button_layout)

        # Bouton 1 - Backoffice Campagnes
        self.backoffice_button = QPushButton("Gestions des campagnes - Accouts view")
        self.backoffice_button.setFont(self.font_manager.get_font("bold", 14))
        self.backoffice_button.setFixedSize(300, 40)
        self.backoffice_button.clicked.connect(self.open_backoffice)
        button_layout.addWidget(self.backoffice_button)
        button_layout.addSpacing(10)  # Espace entre les boutons

        # Bouton 2 - Toutes les campagnes
        self.tool2_button = QPushButton("Trading desk")
        self.tool2_button.setFont(self.font_manager.get_font("bold", 14))
        self.tool2_button.setFixedSize(300, 40)
        self.tool2_button.clicked.connect(self.open_all_campaigns)
        button_layout.addWidget(self.tool2_button)
        button_layout.addSpacing(10)  # Espace entre les boutons

        # Bouton 3 - Outil 3 (ne fait rien)
        self.tool3_button = QPushButton("Gestion des Comptes")
        self.tool3_button.setFont(self.font_manager.get_font("bold", 14))
        self.tool3_button.setFixedSize(300, 40)
        self.tool3_button.clicked.connect(self.open_account_manage)
        button_layout.addWidget(self.tool3_button)
        button_layout.addSpacing(10)  # Espace entre les boutons

        # Bouton 4 - Outil 4 (ne fait rien)
        self.tool4_button = QPushButton("Paramètres système")
        self.tool4_button.setFont(self.font_manager.get_font("bold", 14))
        self.tool4_button.setFixedSize(300, 40)
        self.tool4_button.clicked.connect(self.show_not_implemented)
        button_layout.addWidget(self.tool4_button)
        button_layout.addSpacing(40)  # Espace entre les boutons

        # Version du Saas
        title_label = QLabel("Version actuelle : v1.13")
        title_label.setFont(self.font_manager.get_font("normal", 10))
        title_label.setAlignment(Qt.AlignmentFlag.AlignLeft)
        main_layout.addWidget(title_label)

        # Appliquer un style aux boutons et à la fenêtre
        self.setStyleSheet("""
            QMainWindow {
                background-color: #476AFF;
            }
            QWidget#centralWidget {
                background-color: #476AFF;
            }
            QPushButton {
                background-color: white;
                color: #13100D;
                border-radius: 10px;
                padding: 10px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #f0f0f0;
            }
            QPushButton:pressed {
                background-color: #e0e0e0;
            }
            QLabel {
                color: white;
            }
        """)

        # Définir un nom d'objet pour le widget central pour le cibler dans le CSS
        central_widget.setObjectName("centralWidget")

        # Stocker les références aux fenêtres ouvertes
        self.opened_windows = []

        # Créer le menu et les raccourcis clavier
        self.create_menu()

    def open_backoffice(self):
        # Importer ici pour éviter les imports circulaires
        from ui_accountcampaign.main_window import MainWindow

        self.backoffice_window = MainWindow()
        self.backoffice_window.show()
        self.hide()  # Cache la fenêtre de sélection d'outils

        # Stocker la référence à la fenêtre ouverte
        self.opened_windows.append(self.backoffice_window)

    def open_all_campaigns(self):
        # Importer ici pour éviter les imports circulaires
        from ui_all_campaign.main_window import AllCampaignsWindow

        self.all_campaigns_window = AllCampaignsWindow()
        self.all_campaigns_window.show()
        self.hide()  # Cache la fenêtre de sélection d'outils

        # Stocker la référence à la fenêtre ouverte
        self.opened_windows.append(self.all_campaigns_window)


    def open_account_manage(self):
        # Importer ici pour éviter les imports circulaires
        from ui_account_tools.main_window import MainWindow

        self.open_account_manage_window = MainWindow()
        self.open_account_manage_window.show()
        self.hide()  # Cache la fenêtre de sélection d'outils

        # Stocker la référence à la fenêtre ouverte
        self.opened_windows.append(self.open_account_manage_window)



    def show_not_implemented(self):
        QMessageBox.information(self, "Fonctionnalité non disponible",
                              "Cette fonctionnalité n'est pas encore implémentée.")

    def create_menu(self):
        """Crée le menu de l'application avec les raccourcis clavier."""
        # Créer la barre de menu
        menu_bar = self.menuBar()

        # Menu Fichier
        file_menu = menu_bar.addMenu("Fichier")

        # Action Quitter avec raccourci Command+Q
        quit_action = QAction("Quitter", self)
        quit_action.setShortcut(QKeySequence.StandardKey.Quit)  # Command+Q sur Mac
        quit_action.triggered.connect(self.close)
        file_menu.addAction(quit_action)

def log_exceptions(exctype, value, tb):
    print("Une exception est survenue :", exctype.__name__, value)
    traceback.print_tb(tb)

sys.excepthook = log_exceptions

if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setWindowIcon(QIcon("IconeMACOS.icns"))

    def verify_code():
        # correct_code = "LAb4-dvh8H-R9SfnAISj"
        correct_code = ""  # Code à modifier selon vos besoins

        while True:
            code, ok = QInputDialog.getText(None,
                                          "Vérification",
                                          "Entrez le code d'accès:",
                                          QLineEdit.EchoMode.Password)

            if not ok:  # Si l'utilisateur clique sur Annuler
                sys.exit()

            if code == correct_code:
                return True
            else:
                QMessageBox.warning(None,
                                  "Erreur",
                                  "Code incorrect. Veuillez réessayer.")

    # Vérification du code avant de lancer l'application
    if verify_code():
        font_manager = FontManager()
        font = font_manager.get_font("regular", 12)
        app.setFont(font)

        # Créer et afficher la fenêtre de sélection d'outils
        tool_selection = ToolSelectionWindow(font_manager)
        tool_selection.show()

        sys.exit(app.exec())