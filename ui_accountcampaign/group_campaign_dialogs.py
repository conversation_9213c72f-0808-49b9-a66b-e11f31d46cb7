"""
Dialogues pour la gestion des groupes de campagnes.
"""

from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QLabel, QLineEdit, QPushButton, 
                            QHBoxLayout, QMessageBox)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QPixmap
import os
import sys

class AddGroupCampaignDialog(QDialog):
    def __init__(self, account_id, account_name, project_id, project_name, controller, font_manager):
        super().__init__()

        self.controller = controller
        self.account_id = account_id
        self.account_name = account_name
        self.project_id = project_id
        self.project_name = project_name

        self.setWindowTitle("Creer un nouveau Groupe de Campagne")
        self.setFixedSize(500, 400)

        layout = QVBoxLayout()

        # Labels pour les informations du compte et du projet
        self.label2 = QLabel("Account :")
        self.label22 = QLabel(str(account_name))
        self.label3 = QLabel("- ID Xano:")
        self.label32 = QLabel(str(account_id))
        self.label4 = QLabel("Projet :")
        self.label42 = QLabel(str(project_name))
        self.label5 = QLabel("- Project ID Xano:")
        self.label52 = QLabel(str(project_id))

        self.label2.setFont(font_manager.get_font("bold", 14))
        self.label22.setFont(font_manager.get_font("regular", 14))
        self.label3.setFont(font_manager.get_font("bold", 14))
        self.label32.setFont(font_manager.get_font("regular", 14))
        self.label4.setFont(font_manager.get_font("bold", 14))
        self.label42.setFont(font_manager.get_font("regular", 14))
        self.label5.setFont(font_manager.get_font("bold", 14))
        self.label52.setFont(font_manager.get_font("regular", 14))

        # Layout pour les informations du compte
        row1_layout = QHBoxLayout()
        row1_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)
        row1_layout.addWidget(self.label2)
        row1_layout.addWidget(self.label22)
        row1_layout.addWidget(self.label3)
        row1_layout.addWidget(self.label32)

        # Layout pour les informations du projet
        row2_layout = QHBoxLayout()
        row2_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)
        row2_layout.addWidget(self.label4)
        row2_layout.addWidget(self.label42)
        row2_layout.addWidget(self.label5)
        row2_layout.addWidget(self.label52)

        layout.addLayout(row1_layout)
        layout.addLayout(row2_layout)

        # Champ pour le nom du groupe de campagne
        self.label6 = QLabel("Nom du groupe de campagne :")
        self.label6.setFont(font_manager.get_font("bold", 12))
        self.groupcampaign_name_input = QLineEdit()
        self.label62 = QLabel()  # Pour les messages d'erreur

        row3_layout = QVBoxLayout()
        row3_layout.addWidget(self.label6)
        row3_layout.addWidget(self.groupcampaign_name_input)
        row3_layout.addWidget(self.label62)

        layout.addLayout(row3_layout)

        # Bouton d'ajout
        self.submit_button = QPushButton("Ajouter")
        self.submit_button.clicked.connect(self.post_groupcampaign)
        layout.addWidget(self.submit_button)
        self.setLayout(layout)

    def post_groupcampaign(self):
        """Vérifie les données et envoie le groupe de campagne via le contrôleur."""
        groupcampaign_name = self.groupcampaign_name_input.text().strip()

        # Vérification via le contrôleur
        is_valid, error_message = self.controller.validate_groupcampaign_name(groupcampaign_name)
        if not is_valid:
            self.label62.setText(error_message)
            self.label62.setStyleSheet("color: #FF6464;")
            return

        success, message = self.controller.send_groupcampaign_to_xano(self.project_id, groupcampaign_name)

        if success:
            msg_box = QMessageBox()
            msg_box.setWindowTitle("Succès")
            msg_box.setText("Campagne groupe ajouté avec succès.")
            
            # Définir une icône personnalisée
            if getattr(sys, 'frozen', False):
                base_path = sys._MEIPASS
                assets_path = os.path.join(base_path, "assets", "icons")
            else:
                assets_path = os.path.abspath("assets/icons/")

            icon_path = os.path.join(assets_path, "custom_success.png")
            if os.path.exists(icon_path):
                msg_box.setIconPixmap(QPixmap(icon_path).scaled(48, 48, Qt.AspectRatioMode.KeepAspectRatio))
            msg_box.exec()
            self.accept()  # Ferme la boîte de dialogue
        else:
            QMessageBox.warning(self, "Erreur", f"Échec de l'ajout du campagne groupe : {message}")

        self.controller.update_groupcampaigns_options()


class ModifyGroupCampaignDialog(QDialog):
    def __init__(self, account_id, account_name, project_id, project_name, controller, font_manager, groupcampaing_id, groupcampaing_name):
        super().__init__()

        self.controller = controller
        self.account_id = account_id
        self.account_name = account_name
        self.project_id = project_id
        self.project_name = project_name
        self.groupcampaing_id = groupcampaing_id
        self.groupcampaing_name = groupcampaing_name

        self.setWindowTitle("Modifier un Groupe de Campagne")
        self.setFixedSize(500, 400)

        layout = QVBoxLayout()

        # Labels pour les informations du compte et du projet
        self.label2 = QLabel("Account :")
        self.label22 = QLabel(str(account_name))
        self.label3 = QLabel("- ID Xano:")
        self.label32 = QLabel(str(account_id))
        self.label4 = QLabel("Projet :")
        self.label42 = QLabel(str(project_name))
        self.label5 = QLabel("- Project ID Xano:")
        self.label52 = QLabel(str(project_id))
        self.label7 = QLabel("- Group Campaign ID Xano:")
        self.label72 = QLabel(str(groupcampaing_id))

        self.label2.setFont(font_manager.get_font("bold", 14))
        self.label22.setFont(font_manager.get_font("regular", 14))
        self.label3.setFont(font_manager.get_font("bold", 14))
        self.label32.setFont(font_manager.get_font("regular", 14))
        self.label4.setFont(font_manager.get_font("bold", 14))
        self.label42.setFont(font_manager.get_font("regular", 14))
        self.label5.setFont(font_manager.get_font("bold", 14))
        self.label52.setFont(font_manager.get_font("regular", 14))
        self.label7.setFont(font_manager.get_font("bold", 14))
        self.label72.setFont(font_manager.get_font("regular", 14))

        # Layout pour les informations du compte
        row1_layout = QHBoxLayout()
        row1_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)
        row1_layout.addWidget(self.label2)
        row1_layout.addWidget(self.label22)
        row1_layout.addWidget(self.label3)
        row1_layout.addWidget(self.label32)

        # Layout pour les informations du projet
        row2_layout = QHBoxLayout()
        row2_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)
        row2_layout.addWidget(self.label4)
        row2_layout.addWidget(self.label42)
        row2_layout.addWidget(self.label5)
        row2_layout.addWidget(self.label52)

        # Layout pour les informations du groupe de campagne
        row3_layout = QHBoxLayout()
        row3_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)
        row3_layout.addWidget(self.label7)
        row3_layout.addWidget(self.label72)

        layout.addLayout(row1_layout)
        layout.addLayout(row2_layout)
        layout.addLayout(row3_layout)

        # Champ pour le nom du groupe de campagne
        self.label6 = QLabel("Nom du groupe de campagne :")
        self.label6.setFont(font_manager.get_font("bold", 12))
        self.groupcampaign_name_input = QLineEdit()
        self.groupcampaign_name_input.setText(groupcampaing_name)
        self.label62 = QLabel()  # Pour les messages d'erreur

        row4_layout = QVBoxLayout()
        row4_layout.addWidget(self.label6)
        row4_layout.addWidget(self.groupcampaign_name_input)
        row4_layout.addWidget(self.label62)

        layout.addLayout(row4_layout)

        # Bouton de modification
        self.submit_button = QPushButton("Modifier")
        self.submit_button.clicked.connect(self.modify_groupcampaign)
        layout.addWidget(self.submit_button)
        self.setLayout(layout)

    def modify_groupcampaign(self):
        """Vérifie les données et modifie le groupe de campagne via le contrôleur."""
        groupcampaign_name = self.groupcampaign_name_input.text().strip()

        # Vérification via le contrôleur
        is_valid, error_message = self.controller.validate_groupcampaign_name(groupcampaign_name)
        if not is_valid:
            self.label62.setText(error_message)
            self.label62.setStyleSheet("color: #FF6464;")
            return

        success, message = self.controller.modify_groupcampaign_to_xano(self.project_id, groupcampaign_name, self.groupcampaing_id)

        if success:
            msg_box = QMessageBox()
            msg_box.setWindowTitle("Succès")
            msg_box.setText("Campagne groupe modifié avec succès.")
            
            # Définir une icône personnalisée
            if getattr(sys, 'frozen', False):
                base_path = sys._MEIPASS
                assets_path = os.path.join(base_path, "assets", "icons")
            else:
                assets_path = os.path.abspath("assets/icons/")

            icon_path = os.path.join(assets_path, "custom_success.png")
            if os.path.exists(icon_path):
                msg_box.setIconPixmap(QPixmap(icon_path).scaled(48, 48, Qt.AspectRatioMode.KeepAspectRatio))
            msg_box.exec()
            self.accept()  # Ferme la boîte de dialogue
        else:
            QMessageBox.warning(self, "Erreur", f"Échec de modification du campagne groupe : {message}")

        self.controller.update_groupcampaigns_options()
