pyinstaller --windowed --name="SACorpSaas" --icon=IconeMACOS.icns main.py

pyinstaller --windowed --name="SACorp-RateCard" --icon=IconeMACOS2.icns --add-data="assets:assets" --add-data="New_RateCard_2025_2.csv:."  main.py

pyinstaller --windowed --hidden-import encodings --hidden-import _bootlocale --name="SACorp-RateCard" --icon=IconeMACOS2.icns --add-data="assets:assets" --add-data="New_RateCard_2025_2.csv:."  main.py

pyinstaller --onefile --windowed --hidden-import encodings --hidden-import numpy --hidden-import pandas --hidden-import _bootlocale \
--name="SACorp-SAASbackofficev1_07" --icon=IconeMACOS.icns \
--add-data="assets:assets" \
main.py

pyinstaller --onefile --windowed \
--hidden-import encodings \
--hidden-import numpy \
--hidden-import pandas \
--hidden-import _bootlocale \
--hidden-import boto3 \
--hidden-import botocore \
--hidden-import botocore.vendored \
--hidden-import jmespath \
--hidden-import urllib3 \
--name="SACorp-SAASbackofficev1_13" \
--icon=IconeMACOS.icns \
--add-data="assets:assets" \
main.py



Avant faire ca : export MACOSX_DEPLOYMENT_TARGET=10.15
env MACOSX_DEPLOYMENT_TARGET=10.15 pyenv install 3.10.5



python -m nuitka --standalone --enable-plugin=pyqt6 \
--macos-create-app-bundle \
--include-data-dir=assets=assets \
--include-data-files=New_RateCard_2025_2.csv=New_RateCard_2025.csv \
--macos-app-icon=IconeMACOS2.icns \
--output-dir=dist main.py


hdiutil create -volname "SA Corp Ratecard" -srcfolder "/Users/<USER>/Documents/SA/RateCard-Backoffice/dist/SACorp-RateCard.app" -ov -format UDZO "SA Corp RateCard.dmg"