<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/PyQt6/Qt6/translations/qt_ar.qm</key>
		<data>
		KRRGiDA52ynAP9C/R1Ph6oOCiLA=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_bg.qm</key>
		<data>
		pL4wUSZh7QzIAlbmOsxZyJWmANk=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_ca.qm</key>
		<data>
		hhagY04qgpLvYn87GwVCGElxNrI=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_cs.qm</key>
		<data>
		gX1GFeTg/EAGQvGwRpLWEDn1XHg=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_da.qm</key>
		<data>
		qB/6hrfabhdI1dTdXy+8gTtyw4o=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_de.qm</key>
		<data>
		5sjoR3izOmpbnmXyBCtMeN5HbqA=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_en.qm</key>
		<data>
		K/AFQALI99hd0IDfMyVTv5s6jiY=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_es.qm</key>
		<data>
		jUxtNjW95x/bBtjz6mlWkEid8Ws=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_fa.qm</key>
		<data>
		UeSt6KvJ6HgHvki6qANY+NpaC7s=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_fi.qm</key>
		<data>
		ufvhtLrOvP/SjfxIN6Wy72EhWO8=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_fr.qm</key>
		<data>
		P8xmqZT4uiJFvkGTph86WQ+nLeQ=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_gd.qm</key>
		<data>
		JRI2FUgpR3chduBV5KdAQ7L7yqA=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_gl.qm</key>
		<data>
		615yBTVc/GvLTfJ+IkB5hCyXspY=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_he.qm</key>
		<data>
		+1LsLXAn6rV6GMcjsEjC+tkgkPE=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_help_ar.qm</key>
		<data>
		V1G+loF7tq58nanx+6f0LzHPzF0=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_help_bg.qm</key>
		<data>
		cla+OQuIoFPAJSSIxEO+Qvby2So=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_help_ca.qm</key>
		<data>
		Xi/rn5Hz1xYk0QW/3MzP9jYHVt4=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_help_cs.qm</key>
		<data>
		fwYc8MaZ0SWlUx40gMIZZEUvReo=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_help_da.qm</key>
		<data>
		x38KFIJiqRZ58ZaJ5HkLdU1F1dU=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_help_de.qm</key>
		<data>
		blI6Y8OOppZgmM3RovDtfvIguMw=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_help_en.qm</key>
		<data>
		K/AFQALI99hd0IDfMyVTv5s6jiY=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_help_es.qm</key>
		<data>
		qRiuFaDfGHx3ib6FmagOJ58DmWQ=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_help_fr.qm</key>
		<data>
		48mjB+pHn71bua/OemrK0QnRAls=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_help_gl.qm</key>
		<data>
		WJzgmfLB2SWBtc8OF75Jor8AFNQ=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_help_hr.qm</key>
		<data>
		9KxH3kxpESyJlULlvLBWxN6qS1g=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_help_hu.qm</key>
		<data>
		QjgAUTM35OV2gKVaJvm65rl/vuk=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_help_it.qm</key>
		<data>
		ZZNaZ8c/Gfz2Aj+5UDClrK+dohw=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_help_ja.qm</key>
		<data>
		m3N+0F+A/l082PWIzOwWuxHdNWA=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_help_ka.qm</key>
		<data>
		ntgtBJOS7PtvfYDadneCv9nVX4Q=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_help_ko.qm</key>
		<data>
		rqIuSg0hOWczgCx6tzjd0Dc3t9Y=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_help_nl.qm</key>
		<data>
		XBilR68q3A4KAhAnMG4X5XekBDY=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_help_nn.qm</key>
		<data>
		NQMMNLoLNSxhsFkszNhWkRXX7rw=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_help_pl.qm</key>
		<data>
		igm9OJmFQ7dOJnNHjt1U+0u90Gg=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_help_pt_BR.qm</key>
		<data>
		G1yOhdQK6XUsQosgN/Z+XsDbrL0=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_help_ru.qm</key>
		<data>
		Ogw2mHKxEqFXKqF+64FLFosiXZg=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_help_sk.qm</key>
		<data>
		4+X0WCzF+v5t9DZE0RSEhhAjwIQ=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_help_sl.qm</key>
		<data>
		yU6DJZXsdlNwVTRo+HwC2359E4o=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_help_tr.qm</key>
		<data>
		WeeoTRYcgYKxwKtP3Xq+scXixyo=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_help_uk.qm</key>
		<data>
		XfOyE9mFo7vbR2s3t3gNfX3xfkE=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_help_zh_CN.qm</key>
		<data>
		TtQ/AmgRy0aMnHkOQJjK2FQ1/t0=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_help_zh_TW.qm</key>
		<data>
		ZMyrJ0P8wW5D8Le2g7ws2XbKbtg=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_hr.qm</key>
		<data>
		jM5pEqiQWu8a/L1rlRue+istxoM=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_hu.qm</key>
		<data>
		uGeHNYo3K9DSj/09YSYXLvcsp+Q=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_it.qm</key>
		<data>
		HxX7SC2HFKVO8x6hPBN4xcyBn+s=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_ja.qm</key>
		<data>
		AKGJqOdNjXpTLGNG6Zd0rQNElBA=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_ka.qm</key>
		<data>
		RuPH1lBlSGVQ0s2cO4aK+GzIQpg=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_ko.qm</key>
		<data>
		AnsGXCOBcw2OR4DnRey9IdsZNPo=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_lt.qm</key>
		<data>
		4idmpJYS95FWxVDYPGwjA0XdpDM=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_lv.qm</key>
		<data>
		s/U/wIcFOtrd7RFIqEElnaXxjvA=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_nl.qm</key>
		<data>
		TbrDNGM1xnD1tLufYerFUkf9yFY=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_nn.qm</key>
		<data>
		5FWrnrzNvy59ItO31jY0dL4uz24=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_pl.qm</key>
		<data>
		LHIwfv5qOzNptwC7H1IyAtzA3Tg=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_pt_BR.qm</key>
		<data>
		h1JF5Na/E0R2PaT826u5lF5jms8=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_pt_PT.qm</key>
		<data>
		zBErnJUTvPdJfzQXFotMip92QKk=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_ru.qm</key>
		<data>
		lEygrV9Dssn4qHgu7/HCxUaf2dg=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_sk.qm</key>
		<data>
		BnnDLjLb71JsO4c7Y8aGLN3HdZ0=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_sl.qm</key>
		<data>
		nzyFwRWig+UjDR7q2EyMtzpx+gM=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_sv.qm</key>
		<data>
		NX1M5sr/JDVBrusZ9mRhHPlZ054=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_tr.qm</key>
		<data>
		GNOMeE5IepUdyPQyUALgG0ok3RY=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_uk.qm</key>
		<data>
		m+fwODheG3jzS2si4ZrnrROCKmg=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_zh_CN.qm</key>
		<data>
		9ETcBWcd3c6+IpD3l2/YMWWCxBs=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qt_zh_TW.qm</key>
		<data>
		2dNImYY974KBLyqiBisBNKZmeqA=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qtbase_ar.qm</key>
		<data>
		If0TGyO90bunu7hvPtXIOHb0Vjg=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qtbase_bg.qm</key>
		<data>
		ZUQJzfP1UVVZV9Pbz41qDY8DpsU=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qtbase_ca.qm</key>
		<data>
		vUuLxEqUy1QBErKd+qZKJSgKvNE=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qtbase_cs.qm</key>
		<data>
		Og53dTnFG7Ze52uOHY3OQ4bLyIY=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qtbase_da.qm</key>
		<data>
		cLGbKmkU2n1in1d/iYdVNxPNXT8=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qtbase_de.qm</key>
		<data>
		Fjkj4wQ5jeys2pDB7fPEMO8OkHk=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qtbase_en.qm</key>
		<data>
		K/AFQALI99hd0IDfMyVTv5s6jiY=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qtbase_es.qm</key>
		<data>
		gJ5YDNvy/9oQx3+L6brAgZeMECs=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qtbase_fa.qm</key>
		<data>
		sK2+lQeQkkJCgG9nFxLFe1hLWPs=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qtbase_fi.qm</key>
		<data>
		e1MTzaEmu3hjABSZ+2b7G1bCVfw=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qtbase_fr.qm</key>
		<data>
		Fwg5qTenfFiGFP3zEGPBZd3Ba4Y=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qtbase_gd.qm</key>
		<data>
		JPf/gJ4vEcV5zTiP6lpMVS/41NA=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qtbase_he.qm</key>
		<data>
		2znGuqRDqpuyCAQ+9/t+NAPBLZA=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qtbase_hr.qm</key>
		<data>
		yVpBY4hSHvW9s+5dEenc1M4i670=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qtbase_hu.qm</key>
		<data>
		VNyxc6LzG0rjcm/DTaR1KTrwahY=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qtbase_it.qm</key>
		<data>
		mK16iJ2yBZ0webAzX5vxjegfA14=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qtbase_ja.qm</key>
		<data>
		NYSDN7XwpLM7o5KaTZPdCaL6QXA=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qtbase_ka.qm</key>
		<data>
		DBt0dSK7Kh4yZym7+RpG4pl6Z0A=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qtbase_ko.qm</key>
		<data>
		8Q6IJ2Lc0uYAQb3WzFdZj8PfQ0M=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qtbase_lv.qm</key>
		<data>
		lTjE2LualcDZ3FfHcIqZ3VOjLR8=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qtbase_nl.qm</key>
		<data>
		XM6JLNftkkOwtXkj7pLHmtYmFTs=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qtbase_nn.qm</key>
		<data>
		afxH/zozrcI0FfT4cnIFOZjWFJo=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qtbase_pl.qm</key>
		<data>
		bk6xEg321fmhwLFR/z94cxMhnq8=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qtbase_pt_BR.qm</key>
		<data>
		/RNzQXx+k9Y8l7O+eVbNfdgM6+k=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qtbase_ru.qm</key>
		<data>
		QF9FNhpTfHkjwkDVGw/xxGYhwgM=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qtbase_sk.qm</key>
		<data>
		AhG0kRtbdMwaRsD8qH079WMqpEo=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qtbase_tr.qm</key>
		<data>
		Yj7WJB2UIVXUm8nrS/kphb2OppE=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qtbase_uk.qm</key>
		<data>
		Y6FDJ9DPCUHW1rWL+n6LEDN/VXs=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qtbase_zh_CN.qm</key>
		<data>
		NWH5X2RrIXLS6HCXO22B6YPtAKY=
		</data>
		<key>Resources/PyQt6/Qt6/translations/qtbase_zh_TW.qm</key>
		<data>
		YveJ6PoME8X9T+JQV2v0WwfIV4E=
		</data>
		<key>Resources/assets/IconeMACOS.icns</key>
		<data>
		ypZkf6U6B3cNualReYR+yHujhCI=
		</data>
		<key>Resources/assets/IconeMACOS2.icns</key>
		<data>
		tEoMYaa8aPfLmAVPcjZiF9P9uy8=
		</data>
		<key>Resources/assets/IconeMACOS3.icns</key>
		<data>
		zEDUhl9LzmjryE+YiO4uDQsKTY4=
		</data>
		<key>Resources/assets/IconeMACOS_blanc.png</key>
		<data>
		W2Ih5jL2tOH4Oixtu6mF9RNIi3g=
		</data>
		<key>Resources/assets/IconeMACOS_noir.png</key>
		<data>
		exD8kTir90Il9RPXqUrv12WJuY8=
		</data>
		<key>Resources/assets/font/Gilroy-Bold.ttf</key>
		<data>
		+Rt/Q7dy8giu0rn67KxL43VDf0I=
		</data>
		<key>Resources/assets/font/Gilroy-Light.ttf</key>
		<data>
		DjJvGX2IJWJF7OiZq5WFXMGvixg=
		</data>
		<key>Resources/assets/font/Gilroy-Regular.ttf</key>
		<data>
		Q7MbMMNhAuWqiGg0+uqeJcNfveY=
		</data>
		<key>Resources/assets/font/Gilroy-Thin.ttf</key>
		<data>
		x/WtdYp3aNLpz5K9uiHhpxIV6yo=
		</data>
		<key>Resources/assets/myicon.iconset/icon_128x128.png</key>
		<data>
		qSCbknjmFmC5jnjGe116KVWh9d8=
		</data>
		<key>Resources/assets/myicon.iconset/<EMAIL></key>
		<data>
		TOBTB7YcOu2S36OTCkNobIVAOQA=
		</data>
		<key>Resources/assets/myicon.iconset/icon_16x16.png</key>
		<data>
		tCQWZXpIiItzNw8AUb5zAwnV4Og=
		</data>
		<key>Resources/assets/myicon.iconset/<EMAIL></key>
		<data>
		MnrH3JzsGsAbehwpadrnugnkXe4=
		</data>
		<key>Resources/assets/myicon.iconset/icon_256x256.png</key>
		<data>
		TOBTB7YcOu2S36OTCkNobIVAOQA=
		</data>
		<key>Resources/assets/myicon.iconset/<EMAIL></key>
		<data>
		xeWXNsR4az7SVctq9n06ApqcvaU=
		</data>
		<key>Resources/assets/myicon.iconset/icon_32x32.png</key>
		<data>
		MnrH3JzsGsAbehwpadrnugnkXe4=
		</data>
		<key>Resources/assets/myicon.iconset/<EMAIL></key>
		<data>
		J0WN7gcq4FhLhIej3VQzaz3iyGY=
		</data>
		<key>Resources/assets/myicon.iconset/icon_512x512.png</key>
		<data>
		xeWXNsR4az7SVctq9n06ApqcvaU=
		</data>
		<key>Resources/assets/myicon.iconset/<EMAIL></key>
		<data>
		exD8kTir90Il9RPXqUrv12WJuY8=
		</data>
		<key>Resources/base_library.zip</key>
		<data>
		ilUWyXGd5V4nzuo1gGhehu8PV+I=
		</data>
		<key>Resources/certifi/cacert.pem</key>
		<data>
		1/zOxH96X7xUkiKgZPMFNgFAC28=
		</data>
		<key>Resources/certifi/py.typed</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>Resources/generated-0d6440ac394f8df32e88c130069a00c0b1a0744cf2c3957a52196ebc6a9ad706.icns</key>
		<data>
		GAl8DWSgSlOHpYER8vnI64YCn0Q=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Frameworks/PyQt6/Qt6/lib/QtCore.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			eF2Vhz2ah0+nMrHrEHf5cSCALWc=
			</data>
			<key>requirement</key>
			<string>cdhash H"785d95873d9a874fa732b1eb1077f97120802d67"</string>
		</dict>
		<key>Frameworks/PyQt6/Qt6/lib/QtDBus.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			3ZI0s8891lPGNejfxCNWjc7knfQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"dd9234b3cf3dd653c635e8dfc423568dcee49df4"</string>
		</dict>
		<key>Frameworks/PyQt6/Qt6/lib/QtGui.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			mZ/wHVgc4Ke3hk9r5CFnDB45HKc=
			</data>
			<key>requirement</key>
			<string>cdhash H"999ff01d581ce0a7b7864f6be421670c1e391ca7"</string>
		</dict>
		<key>Frameworks/PyQt6/Qt6/lib/QtNetwork.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			eKwLpTxxwB6tR/rnHkCAn/PpPxo=
			</data>
			<key>requirement</key>
			<string>cdhash H"78ac0ba53c71c01ead47fae71e40809ff3e93f1a"</string>
		</dict>
		<key>Frameworks/PyQt6/Qt6/lib/QtPdf.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			/ZYyboq0dD4LBS8b7F/XaNi3/Mg=
			</data>
			<key>requirement</key>
			<string>cdhash H"fd96326e8ab4743e0b052f1bec5fd768d8b7fcc8"</string>
		</dict>
		<key>Frameworks/PyQt6/Qt6/lib/QtSvg.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			Euv8Ap4VCMDzi04yXTkrryMJCek=
			</data>
			<key>requirement</key>
			<string>cdhash H"12ebfc029e1508c0f38b4e325d392baf230909e9"</string>
		</dict>
		<key>Frameworks/PyQt6/Qt6/lib/QtWidgets.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			iiF2Vi25NqGmdzotJ9io4CTi7Po=
			</data>
			<key>requirement</key>
			<string>cdhash H"8a2176562db936a1a6773a2d27d8a8e024e2ecfa"</string>
		</dict>
		<key>Frameworks/PyQt6/Qt6/plugins/generic/libqtuiotouchplugin.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			Kcc4Z2jd51LzYRx85Hjnp/49oz0=
			</data>
			<key>requirement</key>
			<string>cdhash H"29c7386768dde752f3611c7ce478e7a7fe3da33d"</string>
		</dict>
		<key>Frameworks/PyQt6/Qt6/plugins/iconengines/libqsvgicon.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			I7Gqq/M7k+ZdRIdNlK295JCLonE=
			</data>
			<key>requirement</key>
			<string>cdhash H"23b1aaabf33b93e65d44874d94adbde4908ba271"</string>
		</dict>
		<key>Frameworks/PyQt6/Qt6/plugins/imageformats/libqgif.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			2RgT49J8CIFyB/WwokYzeWwoIQk=
			</data>
			<key>requirement</key>
			<string>cdhash H"d91813e3d27c08817207f5b0a24633796c282109"</string>
		</dict>
		<key>Frameworks/PyQt6/Qt6/plugins/imageformats/libqicns.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			y9WWuIPzL30lUjm8Ks8JLRY40BA=
			</data>
			<key>requirement</key>
			<string>cdhash H"cbd596b883f32f7d255239bc2acf092d1638d010"</string>
		</dict>
		<key>Frameworks/PyQt6/Qt6/plugins/imageformats/libqico.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			lgfrRbiqCZ6Jw64/pkcCDcPuErw=
			</data>
			<key>requirement</key>
			<string>cdhash H"9607eb45b8aa099e89c3ae3fa647020dc3ee12bc"</string>
		</dict>
		<key>Frameworks/PyQt6/Qt6/plugins/imageformats/libqjpeg.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			N+2nZY26kvf6+5iHhLElhYqtZ3E=
			</data>
			<key>requirement</key>
			<string>cdhash H"37eda7658dba92f7fafb988784b125858aad6771"</string>
		</dict>
		<key>Frameworks/PyQt6/Qt6/plugins/imageformats/libqmacheif.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			E+8thNMwB5cuRc658KtNqHoDUr8=
			</data>
			<key>requirement</key>
			<string>cdhash H"13ef2d84d33007972e45ceb9f0ab4da87a0352bf"</string>
		</dict>
		<key>Frameworks/PyQt6/Qt6/plugins/imageformats/libqmacjp2.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			UoXhHJvsgXwq700jZA9AAnoSaRo=
			</data>
			<key>requirement</key>
			<string>cdhash H"5285e11c9bec817c2aef4d23640f40027a12691a"</string>
		</dict>
		<key>Frameworks/PyQt6/Qt6/plugins/imageformats/libqpdf.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			YdeD5wybFSUVZ0s2E1up5n9cnOE=
			</data>
			<key>requirement</key>
			<string>cdhash H"61d783e70c9b152515674b36135ba9e67f5c9ce1"</string>
		</dict>
		<key>Frameworks/PyQt6/Qt6/plugins/imageformats/libqsvg.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			jKw+UC+E4RiSHYSLLF739RHXB4U=
			</data>
			<key>requirement</key>
			<string>cdhash H"8cac3e502f84e118921d848b2c5ef7f511d70785"</string>
		</dict>
		<key>Frameworks/PyQt6/Qt6/plugins/imageformats/libqtga.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			upv2OKA3q6IEud3vXtyK1xHhIRM=
			</data>
			<key>requirement</key>
			<string>cdhash H"ba9bf638a037aba204b9ddef5edc8ad711e12113"</string>
		</dict>
		<key>Frameworks/PyQt6/Qt6/plugins/imageformats/libqtiff.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			83jN2Xr1rzNZ7bujYpfjHIJS8/U=
			</data>
			<key>requirement</key>
			<string>cdhash H"f378cdd97af5af3359edbba36297e31c8252f3f5"</string>
		</dict>
		<key>Frameworks/PyQt6/Qt6/plugins/imageformats/libqwbmp.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			ZuIYm5dfu+OpAYo99nvA3YklG78=
			</data>
			<key>requirement</key>
			<string>cdhash H"66e2189b975fbbe3a9018a3df67bc0dd89251bbf"</string>
		</dict>
		<key>Frameworks/PyQt6/Qt6/plugins/imageformats/libqwebp.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			+vWEQoZsQmsb2EWP9ruCGoV1qps=
			</data>
			<key>requirement</key>
			<string>cdhash H"faf58442866c426b1bd8458ff6bb821a8575aa9b"</string>
		</dict>
		<key>Frameworks/PyQt6/Qt6/plugins/networkinformation/libqscnetworkreachability.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			g2Q1fRVZUhMkSinZL4F2Pud1amo=
			</data>
			<key>requirement</key>
			<string>cdhash H"8364357d15595213244a29d92f81763ee7756a6a"</string>
		</dict>
		<key>Frameworks/PyQt6/Qt6/plugins/platforms/libqcocoa.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			kP9sPR7GoynldmZDASR0oHrvG1A=
			</data>
			<key>requirement</key>
			<string>cdhash H"90ff6c3d1ec6a329e5766643012474a07aef1b50"</string>
		</dict>
		<key>Frameworks/PyQt6/Qt6/plugins/platforms/libqminimal.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			5HBn6XEUBc6jwDuZuQ9ly2iS2k4=
			</data>
			<key>requirement</key>
			<string>cdhash H"e47067e9711405cea3c03b99b90f65cb6892da4e"</string>
		</dict>
		<key>Frameworks/PyQt6/Qt6/plugins/platforms/libqoffscreen.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			bEaATlKenohksWfjGA38qF+IV90=
			</data>
			<key>requirement</key>
			<string>cdhash H"6c46804e529e9e8864b167e3180dfca85f8857dd"</string>
		</dict>
		<key>Frameworks/PyQt6/Qt6/plugins/styles/libqmacstyle.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			6uRBC15204yW5NhHy0zR80rOgxg=
			</data>
			<key>requirement</key>
			<string>cdhash H"eae4410b5e76d38c96e4d847cb4cd1f34ace8318"</string>
		</dict>
		<key>Frameworks/PyQt6/Qt6/plugins/tls/libqcertonlybackend.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			znv3AqbJi9trXDIC4PkIHpKJfmI=
			</data>
			<key>requirement</key>
			<string>cdhash H"ce7bf702a6c98bdb6b5c3202e0f9081e92897e62"</string>
		</dict>
		<key>Frameworks/PyQt6/Qt6/plugins/tls/libqopensslbackend.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			pzGGrlRBXYsiA7v71IPf0Lkj16M=
			</data>
			<key>requirement</key>
			<string>cdhash H"a73186ae54415d8b2203bbfbd483dfd0b923d7a3"</string>
		</dict>
		<key>Frameworks/PyQt6/Qt6/plugins/tls/libqsecuretransportbackend.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			F0skTSNduT5LSrZFoCfKzs7xsQk=
			</data>
			<key>requirement</key>
			<string>cdhash H"174b244d235db93e4b4ab645a027cacecef1b109"</string>
		</dict>
		<key>Frameworks/PyQt6/Qt6/translations</key>
		<dict>
			<key>symlink</key>
			<string>../../../Resources/PyQt6/Qt6/translations</string>
		</dict>
		<key>Frameworks/PyQt6/QtCore.abi3.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			Ks09HKDCGEtEOq04N7c2hrc2WKg=
			</data>
			<key>requirement</key>
			<string>cdhash H"2acd3d1ca0c2184b443aad3837b73686b73658a8"</string>
		</dict>
		<key>Frameworks/PyQt6/QtDBus.abi3.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			UI9cgZWIcB6bgBh317Zo3nZ6AEc=
			</data>
			<key>requirement</key>
			<string>cdhash H"508f5c819588701e9b801877d7b668de767a0047"</string>
		</dict>
		<key>Frameworks/PyQt6/QtGui.abi3.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			qQJAGwn9auT9lMGh0tgYmQgiiCs=
			</data>
			<key>requirement</key>
			<string>cdhash H"a902401b09fd6ae4fd94c1a1d2d818990822882b"</string>
		</dict>
		<key>Frameworks/PyQt6/QtNetwork.abi3.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			3jcOzdkmxaI0HZkGQbUfGy+L0m8=
			</data>
			<key>requirement</key>
			<string>cdhash H"de370ecdd926c5a2341d990641b51f1b2f8bd26f"</string>
		</dict>
		<key>Frameworks/PyQt6/QtWidgets.abi3.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			KbpkYgR8BnDIPGDJCSRM9rI+RpM=
			</data>
			<key>requirement</key>
			<string>cdhash H"29ba6462047c0670c83c60c909244cf6b23e4693"</string>
		</dict>
		<key>Frameworks/PyQt6/sip.cpython-310-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			pLreaXeYB3T1x6p3GDemlO61prY=
			</data>
			<key>requirement</key>
			<string>cdhash H"a4bade6977980774f5c7aa771837a694eeb5a6b6"</string>
		</dict>
		<key>Frameworks/QtCore</key>
		<dict>
			<key>symlink</key>
			<string>PyQt6/Qt6/lib/QtCore.framework/Versions/A/QtCore</string>
		</dict>
		<key>Frameworks/QtDBus</key>
		<dict>
			<key>symlink</key>
			<string>PyQt6/Qt6/lib/QtDBus.framework/Versions/A/QtDBus</string>
		</dict>
		<key>Frameworks/QtGui</key>
		<dict>
			<key>symlink</key>
			<string>PyQt6/Qt6/lib/QtGui.framework/Versions/A/QtGui</string>
		</dict>
		<key>Frameworks/QtNetwork</key>
		<dict>
			<key>symlink</key>
			<string>PyQt6/Qt6/lib/QtNetwork.framework/Versions/A/QtNetwork</string>
		</dict>
		<key>Frameworks/QtPdf</key>
		<dict>
			<key>symlink</key>
			<string>PyQt6/Qt6/lib/QtPdf.framework/Versions/A/QtPdf</string>
		</dict>
		<key>Frameworks/QtSvg</key>
		<dict>
			<key>symlink</key>
			<string>PyQt6/Qt6/lib/QtSvg.framework/Versions/A/QtSvg</string>
		</dict>
		<key>Frameworks/QtWidgets</key>
		<dict>
			<key>symlink</key>
			<string>PyQt6/Qt6/lib/QtWidgets.framework/Versions/A/QtWidgets</string>
		</dict>
		<key>Frameworks/assets</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/assets</string>
		</dict>
		<key>Frameworks/base_library.zip</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/base_library.zip</string>
		</dict>
		<key>Frameworks/certifi</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/certifi</string>
		</dict>
		<key>Frameworks/charset_normalizer/md.cpython-310-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			M1zTBtcA6sxvDeTImbXk+BQvH20=
			</data>
			<key>requirement</key>
			<string>cdhash H"335cd306d700eacc6f0de4c899b5e4f8142f1f6d"</string>
		</dict>
		<key>Frameworks/charset_normalizer/md__mypyc.cpython-310-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			NnrjH2pMck/Tk+3YZWm42qt62wU=
			</data>
			<key>requirement</key>
			<string>cdhash H"367ae31f6a4c724fd393edd86569b8daab7adb05"</string>
		</dict>
		<key>Frameworks/lib-dynload/_bisect.cpython-310-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			GcLTVv+48DXtcbE9eumQ9hrXwRc=
			</data>
			<key>requirement</key>
			<string>cdhash H"19c2d356ffb8f035ed71b13d7ae990f61ad7c117"</string>
		</dict>
		<key>Frameworks/lib-dynload/_blake2.cpython-310-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			5+/KyDDGck8HbVpeeMmD/bgdNPI=
			</data>
			<key>requirement</key>
			<string>cdhash H"e7efcac830c6724f076d5a5e78c983fdb81d34f2"</string>
		</dict>
		<key>Frameworks/lib-dynload/_bz2.cpython-310-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			YmnP597MvLpQVOPgsYLx9tqHKNY=
			</data>
			<key>requirement</key>
			<string>cdhash H"6269cfe7deccbcba5054e3e0b182f1f6da8728d6"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_cn.cpython-310-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			f073q/i5H8I/rlfJKko0/Lw9Mes=
			</data>
			<key>requirement</key>
			<string>cdhash H"7f4ef7abf8b91fc23fae57c92a4a34fcbc3d31eb"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_hk.cpython-310-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			Ikptn7lwn5iF6yIJqBUxNN5X2s8=
			</data>
			<key>requirement</key>
			<string>cdhash H"224a6d9fb9709f9885eb2209a8153134de57dacf"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_iso2022.cpython-310-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			JpktqIl5Je5TmRsDyI326rgewH0=
			</data>
			<key>requirement</key>
			<string>cdhash H"26992da8897925ee53991b03c88df6eab81ec07d"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_jp.cpython-310-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			f70yWTO+R3Ae4dNFDfDkt9gIgQs=
			</data>
			<key>requirement</key>
			<string>cdhash H"7fbd325933be47701ee1d3450df0e4b7d808810b"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_kr.cpython-310-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			c0dcRyQL8DgDoFjz8M0QJFZSoD4=
			</data>
			<key>requirement</key>
			<string>cdhash H"73475c47240bf03803a058f3f0cd10245652a03e"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_tw.cpython-310-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			UE9MFmUdEW2C/mOq8tKlM15c0HM=
			</data>
			<key>requirement</key>
			<string>cdhash H"504f4c16651d116d82fe63aaf2d2a5335e5cd073"</string>
		</dict>
		<key>Frameworks/lib-dynload/_contextvars.cpython-310-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			vJgAVUE8NOtG3guDz5CG9cVBhsw=
			</data>
			<key>requirement</key>
			<string>cdhash H"bc980055413c34eb46de0b83cf9086f5c54186cc"</string>
		</dict>
		<key>Frameworks/lib-dynload/_csv.cpython-310-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			THo81FidrGLYiEcXfbokifJAsdI=
			</data>
			<key>requirement</key>
			<string>cdhash H"4c7a3cd4589dac62d88847177dba2489f240b1d2"</string>
		</dict>
		<key>Frameworks/lib-dynload/_datetime.cpython-310-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			s8M0zJ+2P+UOJQ/UBL+qnVvFeg0=
			</data>
			<key>requirement</key>
			<string>cdhash H"b3c334cc9fb63fe50e250fd404bfaa9d5bc57a0d"</string>
		</dict>
		<key>Frameworks/lib-dynload/_decimal.cpython-310-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			VlZGiynOHsMl9m3pAQqor0DmwzM=
			</data>
			<key>requirement</key>
			<string>cdhash H"5656468b29ce1ec325f66de9010aa8af40e6c333"</string>
		</dict>
		<key>Frameworks/lib-dynload/_hashlib.cpython-310-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			Y3BWfw6qJcjMS1ZWUwDMpYeoLhQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"6370567f0eaa25c8cc4b56565300cca587a82e14"</string>
		</dict>
		<key>Frameworks/lib-dynload/_heapq.cpython-310-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			eIUu7ijAXw4w3HoFFQeWf7adSGM=
			</data>
			<key>requirement</key>
			<string>cdhash H"78852eee28c05f0e30dc7a051507967fb69d4863"</string>
		</dict>
		<key>Frameworks/lib-dynload/_json.cpython-310-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			hGnfQVPdIBTgIgCwZkEanBmcLMc=
			</data>
			<key>requirement</key>
			<string>cdhash H"8469df4153dd2014e02200b066411a9c199c2cc7"</string>
		</dict>
		<key>Frameworks/lib-dynload/_md5.cpython-310-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			g9UjWD18OEjwhkUc+Y0hgr6O94Q=
			</data>
			<key>requirement</key>
			<string>cdhash H"83d523583d7c3848f086451cf98d2182be8ef784"</string>
		</dict>
		<key>Frameworks/lib-dynload/_multibytecodec.cpython-310-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			9wsSf9oHAORspKkxvN9a1PwAZG4=
			</data>
			<key>requirement</key>
			<string>cdhash H"f70b127fda0700e46ca4a931bcdf5ad4fc00646e"</string>
		</dict>
		<key>Frameworks/lib-dynload/_opcode.cpython-310-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			ucqA5yW4o5FA3qrw6rXSFkI3KNI=
			</data>
			<key>requirement</key>
			<string>cdhash H"b9ca80e725b8a39140deaaf0eab5d216423728d2"</string>
		</dict>
		<key>Frameworks/lib-dynload/_pickle.cpython-310-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			gWZbT+LETyxyVc3ABKxAuoU2C5Y=
			</data>
			<key>requirement</key>
			<string>cdhash H"81665b4fe2c44f2c7255cdc004ac40ba85360b96"</string>
		</dict>
		<key>Frameworks/lib-dynload/_posixsubprocess.cpython-310-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			A7agtugyDA2sNYH+eZ4bfF/G9mU=
			</data>
			<key>requirement</key>
			<string>cdhash H"03b6a0b6e8320c0dac3581fe799e1b7c5fc6f665"</string>
		</dict>
		<key>Frameworks/lib-dynload/_queue.cpython-310-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			+mtm/MBD6TfFHFinulyy85aajy0=
			</data>
			<key>requirement</key>
			<string>cdhash H"fa6b66fcc043e937c51c58a7ba5cb2f3969a8f2d"</string>
		</dict>
		<key>Frameworks/lib-dynload/_random.cpython-310-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			lcgg7uvGyhCI8wOdOAmBpjTRLvs=
			</data>
			<key>requirement</key>
			<string>cdhash H"95c820eeebc6ca1088f3039d380981a634d12efb"</string>
		</dict>
		<key>Frameworks/lib-dynload/_scproxy.cpython-310-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			QuEDtLGtG+i+43gJjDiAxWW7/4M=
			</data>
			<key>requirement</key>
			<string>cdhash H"42e103b4b1ad1be8bee378098c3880c565bbff83"</string>
		</dict>
		<key>Frameworks/lib-dynload/_sha1.cpython-310-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			97wLyTU6eZM4N6JuMT33d2urgk0=
			</data>
			<key>requirement</key>
			<string>cdhash H"f7bc0bc9353a79933837a26e313df7776bab824d"</string>
		</dict>
		<key>Frameworks/lib-dynload/_sha256.cpython-310-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			duyaN2pBnNfnc3ZeqkZv/U5g23I=
			</data>
			<key>requirement</key>
			<string>cdhash H"76ec9a376a419cd7e773765eaa466ffd4e60db72"</string>
		</dict>
		<key>Frameworks/lib-dynload/_sha3.cpython-310-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			qVeQLmr8FK3nWjox8WfIxAYUanw=
			</data>
			<key>requirement</key>
			<string>cdhash H"a957902e6afc14ade75a3a31f167c8c406146a7c"</string>
		</dict>
		<key>Frameworks/lib-dynload/_sha512.cpython-310-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			GBN2bvw1v8Hb3vnwFukljItRPhA=
			</data>
			<key>requirement</key>
			<string>cdhash H"1813766efc35bfc1dbdef9f016e9258c8b513e10"</string>
		</dict>
		<key>Frameworks/lib-dynload/_socket.cpython-310-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			AXA8Eib4RD494g6AFPyaO/G8XbU=
			</data>
			<key>requirement</key>
			<string>cdhash H"01703c1226f8443e3de20e8014fc9a3bf1bc5db5"</string>
		</dict>
		<key>Frameworks/lib-dynload/_ssl.cpython-310-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			g+FqnRBZJElLuy8CPkurldcNk64=
			</data>
			<key>requirement</key>
			<string>cdhash H"83e16a9d105924494bbb2f023e4bab95d70d93ae"</string>
		</dict>
		<key>Frameworks/lib-dynload/_statistics.cpython-310-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			Ox7o3Tfn5tJVOGHwX3U0c6NNqFE=
			</data>
			<key>requirement</key>
			<string>cdhash H"3b1ee8dd37e7e6d2553861f05f753473a34da851"</string>
		</dict>
		<key>Frameworks/lib-dynload/_struct.cpython-310-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			+u+G5QM4c7tCVTQG5E+PfhMW1yA=
			</data>
			<key>requirement</key>
			<string>cdhash H"faef86e5033873bb42553406e44f8f7e1316d720"</string>
		</dict>
		<key>Frameworks/lib-dynload/array.cpython-310-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			3sAnY/a9e1FF1MkFJWYAnJr4bpQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"dec02763f6bd7b5145d4c9052566009c9af86e94"</string>
		</dict>
		<key>Frameworks/lib-dynload/binascii.cpython-310-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			AmVdhoMJVP4QdrolvrHaVoU+OGo=
			</data>
			<key>requirement</key>
			<string>cdhash H"02655d86830954fe1076ba25beb1da56853e386a"</string>
		</dict>
		<key>Frameworks/lib-dynload/fcntl.cpython-310-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			IuWWi4dAKNRJf0glVZyMNYAvR/4=
			</data>
			<key>requirement</key>
			<string>cdhash H"22e5968b874028d4497f4825559c8c35802f47fe"</string>
		</dict>
		<key>Frameworks/lib-dynload/grp.cpython-310-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			GwlKIjeps66URNGazRc2bcmgaiI=
			</data>
			<key>requirement</key>
			<string>cdhash H"1b094a2237a9b3ae9444d19acd17366dc9a06a22"</string>
		</dict>
		<key>Frameworks/lib-dynload/math.cpython-310-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			C82N4NjZnQtdDVPLiBIpHZDPQCs=
			</data>
			<key>requirement</key>
			<string>cdhash H"0bcd8de0d8d99d0b5d0d53cb8812291d90cf402b"</string>
		</dict>
		<key>Frameworks/lib-dynload/resource.cpython-310-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			HkFv3eYAdRfkiaZAbaHoypPDjQ0=
			</data>
			<key>requirement</key>
			<string>cdhash H"1e416fdde6007517e489a6406da1e8ca93c38d0d"</string>
		</dict>
		<key>Frameworks/lib-dynload/select.cpython-310-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			3uc8rWO2odRNhuAAhIebdkaHQs8=
			</data>
			<key>requirement</key>
			<string>cdhash H"dee73cad63b6a1d44d86e00084879b76468742cf"</string>
		</dict>
		<key>Frameworks/lib-dynload/termios.cpython-310-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			oCHsh0jXm9Xm3Ujm7G3bZ4O9eTk=
			</data>
			<key>requirement</key>
			<string>cdhash H"a021ec8748d79bd5e6dd48e6ec6ddb6783bd7939"</string>
		</dict>
		<key>Frameworks/lib-dynload/unicodedata.cpython-310-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			WxKm/QMtmdHABA1AYJiQmzU2MuU=
			</data>
			<key>requirement</key>
			<string>cdhash H"5b12a6fd032d99d1c0040d406098909b353632e5"</string>
		</dict>
		<key>Frameworks/lib-dynload/zlib.cpython-310-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			MnITDkksyZPU+ZuPXd9Uw9clMzI=
			</data>
			<key>requirement</key>
			<string>cdhash H"3272130e492cc993d4f99b8f5ddf54c3d7253332"</string>
		</dict>
		<key>Frameworks/libcrypto.3.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			M1Y9VuP7cnR+/qcPC4EA3CPW/4I=
			</data>
			<key>requirement</key>
			<string>cdhash H"33563d56e3fb72747efea70f0b8100dc23d6ff82"</string>
		</dict>
		<key>Frameworks/libpython3.10.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			adxgr0xm6Coq2jbjUSdPX0zyKwE=
			</data>
			<key>requirement</key>
			<string>cdhash H"69dc60af4c66e82a2ada36e351274f5f4cf22b01"</string>
		</dict>
		<key>Frameworks/libssl.3.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			8Kl3ZcqYrWg41nZYygPbVwPM/LE=
			</data>
			<key>requirement</key>
			<string>cdhash H"f0a97765ca98ad6838d67658ca03db5703ccfcb1"</string>
		</dict>
		<key>Resources/PyQt6/Qt6/lib</key>
		<dict>
			<key>symlink</key>
			<string>../../../Frameworks/PyQt6/Qt6/lib</string>
		</dict>
		<key>Resources/PyQt6/Qt6/plugins</key>
		<dict>
			<key>symlink</key>
			<string>../../../Frameworks/PyQt6/Qt6/plugins</string>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_ar.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			sRMzxSDM2ON1zeIJhd9ALVhqGpXgeWMTu+ufcX1Nsi4=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_bg.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			HiLuEm7J92Ax2IAoWpB7OgxFEDkjGf9wDc3ulkq7sa0=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_ca.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			F6smI8f26N70Sz28tRdUQYWBfWXxKY3bEO9xE28jK9w=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_cs.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			xYWRj7HUghoFTqQL3PJ58nXKdsiKV5P0M9rGphGsfo8=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_da.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			9nc8ZyrLc/3vbgkgYNadHl3+6EABK6yRLR6S3ihxQ4Y=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_de.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			BAHDFqo7UKFy60Sk/x7DhJJAc77sfJKO+IjsSORhJog=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_en.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			SUrJorLLL97O01P0qfiY7Y3PYW6bxmdDjGJoHj9/ec8=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_es.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			ZI7UUZvEXXpHeUeadibqFs+aoj6ToLF4VGzEe+oFkas=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_fa.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			P0yYnGspkNgMximUCCFLMWmJI7UvFUSDszcYdd6R+I4=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_fi.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			NC0Bx+6yNOQx0FBoIcTNA8BDMRXY1j/uM1e+IzGFiHk=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_fr.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			yJajiJKpI0awM3k9VkQJkyNrI+R+BvOXSv2wVS9ig4c=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_gd.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			Sd+FWgBKF5UDOK8xRkZvbfTVhSQQvQtY6oDg0CA6nSQ=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_gl.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			IEoBrH3ra1uuGTr+y9HlDRjHO/fZS63rK7/fYSPE7ZM=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_he.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			NvAf/y8l4RbUt694CXt+SVIP1bf7b7Eg5AviKMBcBF8=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_help_ar.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			IiCIyXUtHMO6uYXvLcd+WueFeNzhimHsFbOfAuWIFj0=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_help_bg.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			y83R4LuuMy2A3bCihgVvF8gk+ijTU9f98S/JfZ9v4FQ=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_help_ca.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			jJi17iRuGDl9Dee9drKJF3OpdVU+xdzc5C+KaW5bNN4=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_help_cs.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			SsVvxj5ACUO6sT8dTEGFAhOJCOHUiMJK7mEx09F1Uqo=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_help_da.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			a7CSVSo5hocRn21SFF8Ev4Nzl3RG2PAMDcvVa5aCnw8=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_help_de.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			A3vJmr+YVxrpN1wlE1NsFbVx1mQGw2wd5Ee1R/9TnAc=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_help_en.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			SUrJorLLL97O01P0qfiY7Y3PYW6bxmdDjGJoHj9/ec8=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_help_es.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			/RayefjPaQd/delNkMnAeir/85SKV543ifX/teX0IC0=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_help_fr.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			h17i3Nxk496oiNhASOOC05IaF18ShH3i5rHLEq2a3VA=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_help_gl.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			c5miSGCZdHc/YIZsh7eOp9+8T3UDE9aS94hs12OIPJ8=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_help_hr.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			jIzDxbGrKwdPgpNwAbiWYDQg73VZKzt4FyF3Ng3X6mE=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_help_hu.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			e+MuAVEZyWlFOrtujz+zd5Y6afr7EMJ7eWc3tesoPq8=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_help_it.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			MW/o0IFeK0s5aJW+s47xpAQxkVteBU34D0wM1Vbybks=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_help_ja.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			rmA7LA1DTUDN5DP/y6ZfnuJ5eKnhkxYAe+f+eCpbi0c=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_help_ka.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			FZx0+Us4S17mr984ts/EgGvJns9zvgLUpwIdTOun7H4=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_help_ko.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			8RxkaU6ONOHSxGwaHRXWup8tt7Yd5P31TspauXfD4FI=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_help_nl.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			RKig2vd10UNSGABS0T9iOBTXvfmOLFyAFEt790IU47A=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_help_nn.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			TIpyE5QgeOT3L1ur0mxBt3rOcBiXFKWLVQSn1a0Bk9w=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_help_pl.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			zGy02MVAhiJGcvLknmI8jLfAwc1luNXs1C/JujpgZb0=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_help_pt_BR.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			F6FkBFwPxT0FB8/JDDlw/hr9+hxKjlyIHTT6H4aoIb8=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_help_ru.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			VXtkTm2l8exyDvk5ZWFwh+TR9AsklMxapSTPN5YQjec=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_help_sk.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			9B4z4deQvQ0+sYDx+HW8GR/nR3NijyXCytleFALmaGc=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_help_sl.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			LslV5mJAfrzY3NrlqqIeQQjgtbCu4OnbcSwnBylDU18=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_help_tr.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			bsK4xcd1vHbSWNMlodyXkqtcHY7q8I9BsPk4sCzUZC4=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_help_uk.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			SXz8RzaEaS7kTXo3lej7InDFcGn9nrmKYV3Smrm+inw=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_help_zh_CN.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			4lbR9gNKWlUhxi8Sn5FzEhbkdPceHZyuo9PcsRtnDes=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_help_zh_TW.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			Z+bfCmxl4PzzN5jftBf5u/DN5FErVunjFJql0Y97AZg=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_hr.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			/J5BRvM749CbI0vgHlYjXTA32NgtZPBwC01KbiU6Yac=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_hu.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			wvpknxZ824AdHMDLacHZNZT9W/jAt+eblpk12lw+Gao=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_it.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			nnoYvCDr7sVLqq9ByRinRTRREnH3lt///Q+bnsMcaNM=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_ja.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			D1/lBFKUfR7pdVJJ2GA1S/9pdyebTElqW8d+W4nddF4=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_ka.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			ALZ8bfwlUHm1uYpWQPglGclrJSCgKGgvukB4s510Pqo=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_ko.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			HSVLU14cs1y8rYIQsVuJ9EiBWYJr81tFBGliY90QL6M=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_lt.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			R+tfl0Z992kmFCHVSlvqETHJ+5tjiHkdOLtldDNbZL8=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_lv.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			rd7KKFKCM4KU8kylDTfQil9o03hdCpSvht1OyPKjtEk=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_nl.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			Oehwy9JcD5f6mxgo1eoe2YSYV6LibFMhs/E8dr6PBUg=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_nn.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			TlROI/der+FVPciWlz2yO1eVzSQ3htXGU8wPNqRsp08=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_pl.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			5okK+gyvD22wJI5MlPYBa47IvCZvHg6bjwHo6pEs+Wg=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_pt_BR.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			DiTFjjUnGFP4zfs/D3sHQbWppYjkPN7GKuhaOEJ3Hgo=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_pt_PT.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			LB57v1FopktDdS3UxUdgHAvebWEPhnH6PjrzhZfoR4M=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_ru.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			GziOLW0b1AVgPnlOSYxtfsX1pa2BkkNUb2OZGB2jZ1g=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_sk.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			hAw7Acid+Xb8HS5b/G/IS2Vma4o860y/WqDCKvPSo4M=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_sl.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			xE4DE6lBTMDkkLZbDANvoRvKlZNTsiiIZUe8LISSA08=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_sv.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			ADLIy6956DYCf2RpbQEtOnuJ5fW4JZ4DMbl2OK3zjP8=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_tr.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			WeigxBG+OwgCpoVhsJDxggmda3XE/kbzmOVfEVKOEbk=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_uk.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			BT7B9nfE26SMapfrKPT8eMFREyTRhD1/TOwsfACmYUU=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_zh_CN.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			wOKdMslUCn54ZDt0IU5jPFb2qxHwGOUWYp8sEbH7Cfw=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qt_zh_TW.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			JOImlUSRaJVJoaGZDbO4y3iIgAjYFjsv0kQrdyttG18=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qtbase_ar.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			4D/mjYMgFUNpj9f+Jn3V38W/0ZUUfnT/LxmsNJFAEmM=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qtbase_bg.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			5Eisnj8Wwp6yevMBLv4hBS2qePq/s0zW3/L2nuO9PNs=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qtbase_ca.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			AFsKoMmluTDf3YcGYZWKgGm77IYtdfmLziC/dAG+oT0=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qtbase_cs.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			AwKLQt9UeScDceTDvcffL1bLvm3alWooZKxvZBWGH+g=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qtbase_da.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			fR5cozELVNEEwZvyq9QCs45YTocDmnDhU8Spr3SyXCI=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qtbase_de.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			swBEzCZcchbBorPbjAZf25gMwB8WmQaFnnlms/6rJ1M=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qtbase_en.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			SUrJorLLL97O01P0qfiY7Y3PYW6bxmdDjGJoHj9/ec8=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qtbase_es.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			T/2la6O7VBSrBILR3eZKbyJuNIj2t/PxGhUOAfU/pMg=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qtbase_fa.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			945Ztb3VhhgamZA0ukGIaO0X/pwFcH+2XlI/cOkiU9I=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qtbase_fi.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			5H/hNxPhhNB/pEld3gxYmw6PVi6RV0o1WKk2NEOk+nI=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qtbase_fr.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			MUJDpVwyZ5rUc/3+SNx9ZulzrQ/IvyFkSAxY3dtQwac=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qtbase_gd.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			Y7Q53UQTmqOu1UwuvgP6m8d/IsFO2Puo7/JghEW7Iz0=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qtbase_he.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			4evKFq/omUNW+BygB/vbnd+GWEIBD+kIkj2HO2h8rT8=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qtbase_hr.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			u57smpplLBNA3HXrLnSb5Q3wD4hbPWkA38dnmcRbJE0=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qtbase_hu.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			mEYylVOddpyGRaC3+5WtOEVM/QsU82r0hVE/A23bu8Y=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qtbase_it.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			eAjtVCp4M8S9lOyL37vKALtPL+FZebkSHOMkkyNocwA=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qtbase_ja.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			K5WBxpuXX1m+UFsSfNPBnUA+89EuDoOG+5cjgiPpwd8=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qtbase_ka.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			5V5LcvpiC+vuUpfKWd/i9JiUoeVE8rdIWxz9iCkonW8=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qtbase_ko.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			AXntGxNuHLP1gzUeqixUW6PYOm7j+CwyUFkmoaX18YM=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qtbase_lv.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			hG4EdXOuQMg2ccO6f3PifvwkuYyCcB2g35lz5XQXi7I=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qtbase_nl.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			9hUTvNu2rYAFhflDepXQGKqxf6Eu5BSqMUYv8nnYJA4=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qtbase_nn.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			mDc0tAqk0lBCG6DRYUQWy4tCTW8UD0p9v4sR5l7vY9w=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qtbase_pl.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			PPa1wU1esM+B6a8gZuZrOqV5prmzwyPtZwdh9iVKjFQ=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qtbase_pt_BR.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			g0T1vDM8RdN8/UJaCRJOsR8SQ58zc37AuGg9BKQWdG4=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qtbase_ru.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			PaZgVmj5F40RqDjEUVR4CE3PtPnPIvmdepK0ktucIks=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qtbase_sk.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			1YauLDFAdM85hBf97LQHCdVHjf6wpnwv5g1Qnum1ntc=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qtbase_tr.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			QBPDNA2reuKuWUv7d5oV2eZcjuPns04RB075KVJQ3gI=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qtbase_uk.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			Ubj/VbN9xZB9Y3qN3aEvvoFoUrAkTHTrTw+4SGenhuA=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qtbase_zh_CN.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			mP1Ll9vsivXN0V+FoWwz78dzAbGusEadqvwEuasvNXA=
			</data>
		</dict>
		<key>Resources/PyQt6/Qt6/translations/qtbase_zh_TW.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			IQErK0J/jQnTHOnK5YLKJ5VbR4yj3C7BijBo2AhjjGo=
			</data>
		</dict>
		<key>Resources/PyQt6/QtCore.abi3.so</key>
		<dict>
			<key>symlink</key>
			<string>../../Frameworks/PyQt6/QtCore.abi3.so</string>
		</dict>
		<key>Resources/PyQt6/QtDBus.abi3.so</key>
		<dict>
			<key>symlink</key>
			<string>../../Frameworks/PyQt6/QtDBus.abi3.so</string>
		</dict>
		<key>Resources/PyQt6/QtGui.abi3.so</key>
		<dict>
			<key>symlink</key>
			<string>../../Frameworks/PyQt6/QtGui.abi3.so</string>
		</dict>
		<key>Resources/PyQt6/QtNetwork.abi3.so</key>
		<dict>
			<key>symlink</key>
			<string>../../Frameworks/PyQt6/QtNetwork.abi3.so</string>
		</dict>
		<key>Resources/PyQt6/QtWidgets.abi3.so</key>
		<dict>
			<key>symlink</key>
			<string>../../Frameworks/PyQt6/QtWidgets.abi3.so</string>
		</dict>
		<key>Resources/PyQt6/sip.cpython-310-darwin.so</key>
		<dict>
			<key>symlink</key>
			<string>../../Frameworks/PyQt6/sip.cpython-310-darwin.so</string>
		</dict>
		<key>Resources/QtCore</key>
		<dict>
			<key>symlink</key>
			<string>PyQt6/Qt6/lib/QtCore.framework/Versions/A/QtCore</string>
		</dict>
		<key>Resources/QtDBus</key>
		<dict>
			<key>symlink</key>
			<string>PyQt6/Qt6/lib/QtDBus.framework/Versions/A/QtDBus</string>
		</dict>
		<key>Resources/QtGui</key>
		<dict>
			<key>symlink</key>
			<string>PyQt6/Qt6/lib/QtGui.framework/Versions/A/QtGui</string>
		</dict>
		<key>Resources/QtNetwork</key>
		<dict>
			<key>symlink</key>
			<string>PyQt6/Qt6/lib/QtNetwork.framework/Versions/A/QtNetwork</string>
		</dict>
		<key>Resources/QtPdf</key>
		<dict>
			<key>symlink</key>
			<string>PyQt6/Qt6/lib/QtPdf.framework/Versions/A/QtPdf</string>
		</dict>
		<key>Resources/QtSvg</key>
		<dict>
			<key>symlink</key>
			<string>PyQt6/Qt6/lib/QtSvg.framework/Versions/A/QtSvg</string>
		</dict>
		<key>Resources/QtWidgets</key>
		<dict>
			<key>symlink</key>
			<string>PyQt6/Qt6/lib/QtWidgets.framework/Versions/A/QtWidgets</string>
		</dict>
		<key>Resources/assets/IconeMACOS.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			r4dZ1LUiN4sTjLH0izgXmrR99jTXT7mC8vbVKjbC/TM=
			</data>
		</dict>
		<key>Resources/assets/IconeMACOS2.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			j/uL9Lg9V5TPgIhqZ+KcSRWNOPu2HTsPTyr+JBFdwm8=
			</data>
		</dict>
		<key>Resources/assets/IconeMACOS3.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			kFuTBmmZiYNrMzK7JppZu+8iupg0Ek98hYs1Lm5is58=
			</data>
		</dict>
		<key>Resources/assets/IconeMACOS_blanc.png</key>
		<dict>
			<key>hash2</key>
			<data>
			7CIoJFUNdCR71MxwY/QoC63ZnJpJaxM8girDTdvWULw=
			</data>
		</dict>
		<key>Resources/assets/IconeMACOS_noir.png</key>
		<dict>
			<key>hash2</key>
			<data>
			UFfi396FnqguBFBpKqSEwAuyfqEFU0CmarkSffEMJaM=
			</data>
		</dict>
		<key>Resources/assets/font/Gilroy-Bold.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			G0AdytYK3shwDmNakqklQ9EFnPX9nO1XS2uY85y+g/w=
			</data>
		</dict>
		<key>Resources/assets/font/Gilroy-Light.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			EQRKed56Y/WvJY0EgN5q6IDp958iyAZ5/Du7fMeWoRE=
			</data>
		</dict>
		<key>Resources/assets/font/Gilroy-Regular.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			QxLP4xQDdGbdrQ1qExDlXTF/Mg6AO+RmRkIzY7Y7tCY=
			</data>
		</dict>
		<key>Resources/assets/font/Gilroy-Thin.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			BBY7+SssSmbr/YjLrssElfSNwoG5AaX8PiOnWoan3PI=
			</data>
		</dict>
		<key>Resources/assets/myicon.iconset/icon_128x128.png</key>
		<dict>
			<key>hash2</key>
			<data>
			OzEsL4XuE0bCAkvhWsD1msdTeDuncikdyLK+A+1Iab8=
			</data>
		</dict>
		<key>Resources/assets/myicon.iconset/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			yY2V2GFKOfOSmPHrpPej8/NEO17u60ToOibYfjLAT0g=
			</data>
		</dict>
		<key>Resources/assets/myicon.iconset/icon_16x16.png</key>
		<dict>
			<key>hash2</key>
			<data>
			7B5ZJhK794T9MfXh5M9XRJXqTdKHQDMwi+4pTX5RECg=
			</data>
		</dict>
		<key>Resources/assets/myicon.iconset/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			Lx1M/bRk2h+1jZGpnuRRdVj+w7lG51wz538Q5VQkgTQ=
			</data>
		</dict>
		<key>Resources/assets/myicon.iconset/icon_256x256.png</key>
		<dict>
			<key>hash2</key>
			<data>
			yY2V2GFKOfOSmPHrpPej8/NEO17u60ToOibYfjLAT0g=
			</data>
		</dict>
		<key>Resources/assets/myicon.iconset/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			aWCNkF+G2ytSJaw9VwhXjAV/zzuYQ+0vyMWvk/ogyis=
			</data>
		</dict>
		<key>Resources/assets/myicon.iconset/icon_32x32.png</key>
		<dict>
			<key>hash2</key>
			<data>
			Lx1M/bRk2h+1jZGpnuRRdVj+w7lG51wz538Q5VQkgTQ=
			</data>
		</dict>
		<key>Resources/assets/myicon.iconset/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			uhVO7RleNkSZw04oh42ZKqviMfnVhsp3Wh9bO+0K1kc=
			</data>
		</dict>
		<key>Resources/assets/myicon.iconset/icon_512x512.png</key>
		<dict>
			<key>hash2</key>
			<data>
			aWCNkF+G2ytSJaw9VwhXjAV/zzuYQ+0vyMWvk/ogyis=
			</data>
		</dict>
		<key>Resources/assets/myicon.iconset/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			UFfi396FnqguBFBpKqSEwAuyfqEFU0CmarkSffEMJaM=
			</data>
		</dict>
		<key>Resources/base_library.zip</key>
		<dict>
			<key>hash2</key>
			<data>
			qAF+CkhDLZLB8+lxTf7QVdJBdYDnxNrafi1cyzKwJdQ=
			</data>
		</dict>
		<key>Resources/certifi/cacert.pem</key>
		<dict>
			<key>hash2</key>
			<data>
			xVsh+Qf3+G1IrdCTVS+1ZRdJ/1+GBQjMu0I9bB+9gMc=
			</data>
		</dict>
		<key>Resources/certifi/py.typed</key>
		<dict>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>Resources/charset_normalizer</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/charset_normalizer</string>
		</dict>
		<key>Resources/generated-0d6440ac394f8df32e88c130069a00c0b1a0744cf2c3957a52196ebc6a9ad706.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			CceaJpXqfgwOC9kO7mPbrER3J6s2BX/6vv+55Zxh0y0=
			</data>
		</dict>
		<key>Resources/lib-dynload</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/lib-dynload</string>
		</dict>
		<key>Resources/libcrypto.3.dylib</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/libcrypto.3.dylib</string>
		</dict>
		<key>Resources/libpython3.10.dylib</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/libpython3.10.dylib</string>
		</dict>
		<key>Resources/libssl.3.dylib</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/libssl.3.dylib</string>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
