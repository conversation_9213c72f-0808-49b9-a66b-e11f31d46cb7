"""
Widget pour les filtres de campagnes dans la vue de toutes les campagnes.
"""

from PyQt6.QtWidgets import QWidget, QHBoxLayout, QLineEdit, QComboBox, QPushButton, QDateEdit
from PyQt6.QtCore import Qt, QDate
from PyQt6.QtGui import QStandardItemModel


class CampaignFiltersWidget(QWidget):
    def __init__(self, campaigns_table_widget):
        super().__init__()
        self.campaigns_table_widget = campaigns_table_widget
        self.layout = QHBoxLayout()
        self.layout.setSpacing(10)

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Rechercher une campagne")
        self.layout.addWidget(self.search_input)

        self.levier_filter = QComboBox()
        self.levier_filter.addItem("Levier")
        self.layout.addWidget(self.levier_filter)

        self.objectif_filter = QComboBox()
        self.objectif_filter.addItem("Objectif")
        self.layout.addWidget(self.objectif_filter)

        self.status_filter = QComboBox()
        self.status_filter.addItem("Statut")
        self.layout.addWidget(self.status_filter)

        self.start_date_filter = QDateEdit()
        self.start_date_filter.setCalendarPopup(True)
        self.start_date_filter.setButtonSymbols(QDateEdit.ButtonSymbols.NoButtons)
        self.start_date_filter.setDisplayFormat("yyyy-MM-dd")
        self.start_date_filter.setSpecialValueText("Date de début")
        self.start_date_filter.setDate(QDate.currentDate())
        self.layout.addWidget(self.start_date_filter)
        self.search_input.setStyleSheet("""
            QLineEdit {
                outline: none;
                border: 1px solid gray;
                border-radius: 5px;
                padding: 2px;
            }
            QLineEdit:focus {
                border: 1px solid #888;
            }
        """)
        self.start_date_filter.setStyleSheet("""
            QDateEdit {
                outline: none;
                border: 1px solid gray;
                border-radius: 5px;
                padding: 2px;
            }
            QDateEdit:focus {
                border: 1px solid #888;
            }

            QCalendarWidget QWidget {
                background-color: white;
                color: black;

            }

            QCalendarWidget QToolButton {
                background-color: none;
                color: black;
                font-weight: bold;
                border: none;
                margin: 5px;
            }

            QCalendarWidget QToolButton:hover {
                background-color: #eeeeee;
            }

            QCalendarWidget QSpinBox {
                width: 70px;
                font-size: 12px;
                color: black;
                background-color: none;
                border: none;
            }

            QCalendarWidget QMenu {
                background-color: white;
                border: 1px solid gray;
            }

            QCalendarWidget QAbstractItemView {
                selection-background-color: #476AFF;
                selection-color: white;
                background-color: #fefefe;
                gridline-color: lightgray;
            }

            QCalendarWidget QHeaderView::section {
                background-color: #476AFF;
                color: white;
                font-weight: bold;
            }

            QCalendarWidget QAbstractItemView::item {
                color: black;
            }

            QCalendarWidget QAbstractItemView::item:enabled:nth-child(7),  /* Saturday */
            QCalendarWidget QAbstractItemView::item:enabled:nth-child(1)   /* Sunday */
            {
                color: #476AFF;
            }
            QCalendarWidget QToolButton#qt_calendar_prevmonth {
                qproperty-icon: url("assets/icons/arrow-circle-left.png");
                qproperty-iconSize: 20px 20px;
            }

            QCalendarWidget QToolButton#qt_calendar_nextmonth {
                qproperty-icon: url("assets/icons/arrow-circle-right.png");
                qproperty-iconSize: 20px 20px;
            }


        """)
        self.start_date_filter.lineEdit().setReadOnly(True)
        self.start_date_filter.lineEdit().setCursor(Qt.CursorShape.ArrowCursor)
        self.reset_button = QPushButton("Réinitialiser")
        self.reset_button.clicked.connect(self.reset_filters)
        self.layout.addWidget(self.reset_button)
        self.setLayout(self.layout)

        self.search_input.textChanged.connect(self.filter_table)
        self.start_date_filter.dateChanged.connect(self.filter_table)
        self.levier_filter.currentIndexChanged.connect(self.filter_table)
        self.objectif_filter.currentIndexChanged.connect(self.filter_table)
        self.status_filter.currentIndexChanged.connect(self.filter_table)


    def update_filters_from_table(self, model: QStandardItemModel):
        self._populate_filter(self.levier_filter, model, 2,"Levier")
        self._populate_filter(self.objectif_filter, model, 3,'Objectif')
        self._populate_filter(self.status_filter, model, 6,"Statut")

    def _populate_filter(self, combo: QComboBox, model: QStandardItemModel, column: int,text):
        existing_values = set()
        for row in range(model.rowCount()):
            value = model.item(row, column).text()
            if value not in existing_values:
                existing_values.add(value)
        combo.clear()
        combo.addItem(text)
        combo.addItems(sorted(existing_values))

    def get_layout(self):
        return self.layout

    def filter_table(self):
        model = self.campaigns_table_widget.model
        search_text = self.search_input.text().lower()
        selected_start_date = self.start_date_filter.date().toString("yyyy-MM-dd")

        for row in range(model.rowCount()):
            show_row = True
            if self.levier_filter.currentIndex() > 0:
                val = model.item(row, 2).text()
                if val != self.levier_filter.currentText():
                    show_row = False
            if self.objectif_filter.currentIndex() > 0:
                val = model.item(row, 3).text()
                if val != self.objectif_filter.currentText():
                    show_row = False
            if self.status_filter.currentIndex() > 0:
                val = model.item(row, 6).text()
                if val != self.status_filter.currentText():
                    show_row = False
            if search_text:
                campaign_name = model.item(row, 1).text().lower()
                if search_text not in campaign_name:
                    show_row = False
            # Compare start date only if selected date is not today's date
            if self.start_date_filter.date() != QDate.currentDate():
                row_start_date = model.item(row, 7).text()
                if row_start_date < selected_start_date:
                    show_row = False

            self.campaigns_table_widget.get_table().setRowHidden(row, not show_row)

    def reset_filters(self):
        self.search_input.clear()
        self.levier_filter.setCurrentIndex(0)
        self.objectif_filter.setCurrentIndex(0)
        self.status_filter.setCurrentIndex(0)
        self.start_date_filter.setDate(QDate.currentDate())
