"""
Dialogues pour la gestion des comptes.
"""

from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QLabel, QLineEdit, QPushButton, 
                            QHBoxLayout, QFileDialog, QMessageBox, QTableView)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QPixmap, QStandardItemModel, QStandardItem
import os

class AddAccountDialog(QDialog):
    def __init__(self, controller, font_manager):
        super().__init__()

        self.controller = controller
        self.image_path = None

        self.setWindowTitle("Creer un nouveau Compte")
        self.setFixedSize(500, 400)

        layout = QVBoxLayout()

        # Label et champ pour le nom du projet
        self.label2 = QLabel("Creer un nouveau compte :")
        self.label2.setFont(font_manager.get_font("bold", 14))

        self.label4 = QLabel("Nom du Compte :")
        self.label4.setFont(font_manager.get_font("bold", 12))
        self.label42 = QLabel()
        self.account_name_input = QLineEdit()

        row1_layout = QHBoxLayout()
        row1_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)  # Alignement à gauche
        row1_layout.addWidget(self.label2)

        row3_layout = QVBoxLayout()
        row3_layout.addWidget(self.label4)
        row3_layout.addWidget(self.account_name_input)
        row3_layout.addWidget(self.label42)

        layout.addLayout(row1_layout)
        layout.addLayout(row3_layout)

        # Bouton pour sélectionner une image
        self.image_button = QPushButton("Charger une image")
        self.image_button.clicked.connect(self.load_image)
        layout.addWidget(self.image_button)

        # Label pour afficher l'aperçu de l'image
        self.image_preview = QLabel()
        self.image_preview.setFixedSize(150, 150)  # Taille de l'aperçu
        self.image_preview.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.image_preview.setStyleSheet("border: 1px solid gray;")  # Ajouter une bordure
        
        # Créer un layout centré pour l'image
        image_layout = QHBoxLayout()
        image_layout.addStretch()  # Ajoute un espace flexible à gauche
        image_layout.addWidget(self.image_preview, alignment=Qt.AlignmentFlag.AlignCenter)  # Ajoute l'image centrée
        image_layout.addStretch()  # Ajoute un espace flexible à droite

        layout.addLayout(image_layout)

        self.label5 = QLabel()
        layout.addWidget(self.label5)

        # Bouton d'ajout
        self.submit_button = QPushButton("Créer le compte")
        self.submit_button.clicked.connect(self.post_account)  # Ferme la fenêtre sur validation
        layout.addWidget(self.submit_button)
        self.setLayout(layout)

    def load_image(self):
        """Ouvre un QFileDialog pour sélectionner une image et vérifie si elle est carrée"""
        file_dialog = QFileDialog()
        file_dialog.setNameFilters(["Images (*.png *.jpg *.jpeg)"])
        file_dialog.setViewMode(QFileDialog.ViewMode.List)

        if file_dialog.exec():
            selected_files = file_dialog.selectedFiles()
            if selected_files:
                self.image_path = selected_files[0]  # Stocke le chemin de l'image
                file_size = os.path.getsize(self.image_path)  # Récupère la taille en octets

                max_size = 2000 * 1024  # 500 KB en octets
                pixmap = QPixmap(self.image_path)
                self.label5.setText("")

                if file_size > max_size:
                    self.label5.setText(f"L'image est trop grande ({file_size/1024:.1f} KB). Maximum: {max_size/1024:.1f} KB.")
                    self.label5.setStyleSheet("color: #FF6464;")  # Message en rouge
                    self.image_path = None  # Réinitialise le chemin
                    return

                # Vérifier si l'image est carrée
                if pixmap.width() != pixmap.height():
                    self.label5.setText("L'image n'est pas carrée. Veuillez choisir une image carrée.")
                    self.label5.setStyleSheet("color: #FF6464;")  # Message en rouge
                    self.image_path = None  # Réinitialise le chemin
                else:
                    # Afficher l'aperçu de l'image
                    self.image_preview.setPixmap(pixmap.scaled(
                        self.image_preview.width(), 
                        self.image_preview.height(),
                        Qt.AspectRatioMode.KeepAspectRatio,
                        Qt.TransformationMode.SmoothTransformation
                    ))

    def post_account(self):
        """Vérifie les données et envoie le projet via le contrôleur."""
        account_name = self.account_name_input.text().strip()

        # Vérification via le contrôleur
        is_valid, error_message = self.controller.validate_project_name(account_name)
        if not is_valid:
            self.label42.setText(error_message)
            self.label42.setStyleSheet("color: #FF6464;")
            return

        # Envoi des données via le contrôleur
        self.image_path = self.image_path if hasattr(self, 'image_path') and self.image_path else None

        success, message = self.controller.send_account_to_xano(account_name, self.image_path)

        if success:
            QMessageBox.information(self, "Succès", "Compte ajouté avec succès.")
            self.controller.update_account_options()
            self.accept()  # Ferme la boîte de dialogue
        else:
            QMessageBox.warning(self, "Erreur", f"Échec de l'ajout du compte : {message}")


class UserAccountDialog(QDialog):
    def __init__(self, account_id, account_name, controller, font_manager):
        super().__init__()

        self.controller = controller
        self.account_id = account_id
        self.account_name = account_name

        self.setWindowTitle("Utilisateur")
        self.setFixedSize(800, 400)

        layout = QVBoxLayout()

        # Label et champ pour le nom du projet
        self.label2 = QLabel("Account :")
        self.label22 = QLabel(str(account_name))
        self.label3 = QLabel("- ID Xano:")
        self.label32 = QLabel(str(account_id))

        self.label2.setFont(font_manager.get_font("bold", 14))
        self.label22.setFont(font_manager.get_font("regular", 14))
        self.label3.setFont(font_manager.get_font("bold", 14))
        self.label32.setFont(font_manager.get_font("regular", 14))

        row1_layout = QHBoxLayout()
        row1_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)  # Alignement à gauche
        row1_layout.addWidget(self.label2)
        row1_layout.addWidget(self.label22)
        row1_layout.addWidget(self.label3)
        row1_layout.addWidget(self.label32)

        layout.addLayout(row1_layout)

        # Création du modèle avec colonnes
        self.model = QStandardItemModel(0, 6)  # 0 lignes, 6 colonnes
        self.model.setHorizontalHeaderLabels(["ID", "Email", "Nom", "Prénom", "Entreprise", "Rôle"])

        # Création du tableau
        self.table = QTableView()
        self.table.setSortingEnabled(True)  # Active le tri au clic
        self.table.setModel(self.model)
        self.table.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.table.setSelectionMode(QTableView.SelectionMode.SingleSelection)
        self.table.setStyleSheet("""
            QTableView::item:selected {
            background-color: #476AFF;
            color: white;
            }
        """)

        # Layout principal
        table_layout = QVBoxLayout()
        table_layout.addWidget(self.table)
        layout.addLayout(table_layout)
        self.controller.update_user_options(self.account_id, self.model)

        # Bouton d'ajout
        self.quit_button = QPushButton("Quitter")
        self.quit_button.clicked.connect(self.accept)  # Ferme la fenêtre sur validation
        layout.addWidget(self.quit_button)
        self.setLayout(layout)
