"""
Constantes et utilitaires communs pour l'interface utilisateur.
"""

from PyQt6.QtWidgets import QWidget, QVBoxLayout, QLabel, QComboBox, QPushButton
from PyQt6.QtCore import Qt

# Mapping des leviers et objectifs
LEVIER_OBJECTIFS_MAP = {
    "MetaAds": ["Trafic", "Notoriété", "Vues de vidéo", "Vente", "Interactions", "Visites de profil"],
    "SnapchatAds": ["Trafic", "Notoriété", "Vues de vidéo", "Vente"],
    "TiktokAds": ["Trafic", "Notorié<PERSON>", "Vues de vidéo", "Vente", "Interactions", "Followers"],
    "GoogleAds": ["Search", "Display"],
    "YoutubeAds": ["Search", "Preroll", "Bumper", "Discovery", "YT for Action"],
    "SpotifyAds": ["Trafic", "Notoriété", "Vues de vidéo", "Streams"]
}

# Fonction utilitaire pour convertir le format de date
def convert_date_format(date_str):
    """Convertit une date du format YYYY-MM-DD au format DD/MM/YYYY."""
    if not date_str or len(date_str) != 10:
        return date_str
    try:
        year, month, day = date_str.split('-')
        return f"{day}/{month}/{year}"
    except:
        return date_str
