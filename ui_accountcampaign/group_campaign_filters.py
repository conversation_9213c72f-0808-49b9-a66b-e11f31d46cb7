"""
Widget pour les filtres de campagnes.
"""

from PyQt6.QtWidgets import <PERSON>Widget, QHBoxLayout, QLineEdit, QComboBox, QPushButton, QDateEdit
from PyQt6.QtCore import Qt, QDate
from PyQt6.QtGui import QStandardItemModel


class GroupCampaignFiltersWidget(QWidget):
    def __init__(self, group_campaigns_table_widget):
        super().__init__()
        self.group_campaigns_table_widget = group_campaigns_table_widget
        self.layout = QHBoxLayout()
        self.layout.setSpacing(10)

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Rechercher un groupe de campagne")
        self.layout.addWidget(self.search_input)


        self.status_filter = QComboBox()
        self.status_filter.addItem("Statut")
        self.status_filter.addItem("Active")
        self.status_filter.addItem("Term<PERSON><PERSON>")
        self.layout.addWidget(self.status_filter)

        self.reset_button = QPushButton("Réinitialiser")
        self.reset_button.clicked.connect(self.reset_filters)
        self.layout.addWidget(self.reset_button)

        
        self.setLayout(self.layout)

        self.search_input.textChanged.connect(self.filter_table)
        self.status_filter.currentIndexChanged.connect(self.filter_table)




    def get_layout(self):
        return self.layout

    def filter_table(self):
        model = self.group_campaigns_table_widget.model
        search_text = self.search_input.text().lower()
        

        for row in range(model.rowCount()):
            show_row = True
            if self.status_filter.currentIndex() > 0:
                val = model.item(row, 6).text()
                if val != self.status_filter.currentText():
                    show_row = False
            if search_text:
                group_campaign_name = model.item(row, 2).text().lower()
                if search_text not in group_campaign_name:
                    show_row = False


            self.group_campaigns_table_widget.get_table().setRowHidden(row, not show_row)

    def reset_filters(self, *args):
        """Réinitialise tous les filtres à leur état par défaut."""
        self.search_input.clear()
        self.status_filter.setCurrentIndex(0)
        
        # Appliquer les filtres réinitialisés
        self.filter_table()

