import json
from pathlib import Path


class PresetManager:
    def __init__(self, filename="column_presets.sacorp", folder="SACorp-SAASbackoffice"):
        self.file_path = Path.home() / "Documents" / folder / filename
        self.file_path.parent.mkdir(parents=True, exist_ok=True)

    def load_presets(self):
        if self.file_path.exists():
            with open(self.file_path, "r") as f:
                return json.load(f)
        return {}

    def save_preset(self, name, data):
        presets = self.load_presets()
        presets[name] = data
        with open(self.file_path, "w") as f:
            json.dump(presets, f, indent=2)

    def get_preset(self, name):
        return self.load_presets().get(name)

    def get_all_preset_names(self):
        return list(self.load_presets().keys())
    
    def delete_preset(self, name):
        presets = self.load_presets()
        if name in presets:
            del presets[name]
            with open(self.file_path, "w") as f:
                json.dump(presets, f, indent=2)