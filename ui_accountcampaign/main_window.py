"""
Fenêtre principale de l'application.
"""

from PyQt6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QMessageBox, QMainWindow,QLabel
from PyQt6.QtCore import Qt

from .account_widget import AccountWidget
from .project_widget import ProjectWidget
from .group_campaign_table import GroupCampaignTableWidget
from .campaigns_table import CampaignsTableWidget
from .campaign_filters import CampaignFiltersWidget
from .group_campaign_filters import GroupCampaignFiltersWidget


class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        from controller import DataController  # Import ici pour éviter les imports circulaires
        self.controller = DataController(self)  # Contrôleur pour gérer les requêtes
        from tools.font_manager import FontManager
        self.font_manager = FontManager()
        self.init_ui()

    def init_ui(self):
        self.setWindowTitle("SA Corp - Saas Backoffice")
        self.setGeometry(100, 100, 1600, 700)
        self.setFont(self.font_manager.get_font("regular", 12))

        # Créer un widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Créer la barre de menu
        self.create_menu_bar()

        # Layout principal pour le widget central
        layout = QVBoxLayout(central_widget)

        # Layout de navigation supérieure
        topnav_layout = QHBoxLayout()
        topnav_layout.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignTop)
        topnav_left_layout = QVBoxLayout()
        topnav_lefttop_layout = QHBoxLayout()
        topnav_lefttop_layout.setAlignment(Qt.AlignmentFlag.AlignTop)  # S'assurer que tout est aligné en haut

        # Widgets pour les comptes et projets
        self.account_widget = AccountWidget(self.font_manager)
        topnav_lefttop_layout.addWidget(self.account_widget.get_layout())
        self.project_widget = ProjectWidget(self.font_manager)
        topnav_lefttop_layout.addWidget(self.project_widget.get_layout())
        topnav_lefttop_layout.addSpacing(20)  # Ajoute un peu d'espace entre les éléments
        topnav_left_layout.addLayout(topnav_lefttop_layout)

        topnav_leftbottom_layout = QHBoxLayout()
        # Label pour afficher le nom du groupe de campagne sélectionné
        self.group_campaign_label_indicator = QLabel("Groupe de campagne sélectionné :")
        self.group_campaign_label_indicator.setFont(self.font_manager.get_font("regular", 14))
        self.group_campaign_label = QLabel("")
        self.group_campaign_label.setFont(self.font_manager.get_font("bold", 14))
        topnav_leftbottom_layout.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignBottom)
        topnav_leftbottom_layout.addWidget(self.group_campaign_label_indicator)
        topnav_leftbottom_layout.addWidget(self.group_campaign_label)
        topnav_left_layout.addLayout(topnav_leftbottom_layout)
        




        topnav_layout.addLayout(topnav_left_layout)

        # Tableau des groupes de campagnes

        groupcampaign_layout = QVBoxLayout()

        self.group_campaign_table = GroupCampaignTableWidget(self.font_manager, self.controller)
        self.group_campaignsfilter = GroupCampaignFiltersWidget(self.group_campaign_table)
        groupcampaign_layout.addWidget(self.group_campaignsfilter)
        groupcampaign_layout.addWidget(self.group_campaign_table)
        topnav_layout.addLayout(groupcampaign_layout)

        layout.addLayout(topnav_layout)

        # Tableau des campagnes et filtres
        self.campaigns_table = CampaignsTableWidget(self.font_manager, self.controller)
        # self.campaignsfilter = CampaignFiltersWidget(self.campaigns_table)
        # layout.addWidget(self.campaignsfilter)
        layout.addWidget(self.campaigns_table)

        # Charger les premiers choix
        self.controller.load_initial_options()

        # Connecter le premier dropdown au changement
        self.account_widget.accounts_dropdown.currentIndexChanged.connect(
            lambda _: self.controller.update_projects_options() if self.account_widget.accounts_dropdown.count() > 0 else None
        )
        self.project_widget.projects_dropdown.currentIndexChanged.connect(self.controller.update_groupcampaigns_options)

        self.group_campaign_table.get_table().selectionModel().selectionChanged.connect(
            self.controller.update_campaigns_options
        )
        self.project_widget.projects_dropdown.currentIndexChanged.connect(
            lambda: self.group_campaignsfilter.reset_filters()
        )

        #Account
        self.account_widget.add_account_button.clicked.connect(
            lambda: self.account_widget.open_add_account_window(self.controller)
        )
        self.account_widget.user_account_button.clicked.connect(
            lambda: self.account_widget.open_user_account_window(self.account_id_selected,self.account_name_selected,self.controller)
        )

        #project

        self.project_widget.add_project_button.clicked.connect(
            lambda: self.project_widget.open_add_project_window(self.account_id_selected, self.account_name_selected,self.controller)
        )
        #Group campaign
        self.group_campaign_table.add_group_button.clicked.connect(
            lambda: self.group_campaign_table.open_add_group_window(self.account_id_selected, self.account_name_selected,self.project_id_selected, self.project_name_selected,self.controller)
        )
        self.group_campaign_table.delete_group_button.clicked.connect(
            lambda: self.group_campaign_table.del_groupcampaign(self.project_id_selected)
        )
        self.group_campaign_table.modify_group_button.clicked.connect(
            lambda: self.group_campaign_table.open_modify_group_window(self.account_id_selected, self.account_name_selected,self.project_id_selected, self.project_name_selected,self.controller)
        )
        # Connecter le changement de sélection au label
        self.group_campaign_table.get_table().selectionModel().selectionChanged.connect(
            lambda: self.group_campaign_label.setText(
                self.group_campaign_table.get_table().currentIndex().siblingAtColumn(2).data() or ""
            )
        )

        #campagne
        self.campaigns_table.create_button.clicked.connect(
            lambda: self.campaigns_table.open_add_campaign_window(self.account_id_selected, self.account_name_selected,self.project_id_selected, self.project_name_selected,self.group_id_selected,self.group_name_selected,self.controller)
        )

        self.campaigns_table.modify_button.clicked.connect(
            lambda: self.campaigns_table.open_modify_campaign_window(self.account_id_selected, self.account_name_selected,self.project_id_selected, self.project_name_selected,self.group_id_selected,self.group_name_selected,self.controller)
        )

        self.campaigns_table.view_button.clicked.connect(
            lambda: self.campaigns_table.open_view_campaign_window(self.account_id_selected, self.account_name_selected,self.project_id_selected, self.project_name_selected,self.group_id_selected,self.group_name_selected,self.controller)
        )







    def closeEvent(self, event):
        print("App fermée proprement")
        # Arrêter les timers, threads, sauvegarder l’état, etc.
        event.accept()

    def show_message(self, title, message):
        """Affiche un message à l'utilisateur."""
        QMessageBox.information(self, title, message)

    def create_menu_bar(self):
        """Crée la barre de menu de l'application."""
        menu_bar = self.menuBar()

        # Menu Fichier
        file_menu = menu_bar.addMenu("Fichier")

        # Action Retour au menu principal
        main_menu_action = file_menu.addAction("Retour au menu principal")
        main_menu_action.triggered.connect(self.return_to_main_menu)

        # Action Réinitialiser
        reset_action = file_menu.addAction("Réinitialiser l'application")
        reset_action.triggered.connect(self.reset_application)

        # Ajouter un séparateur
        file_menu.addSeparator()

        # Action Quitter
        quit_action = file_menu.addAction("Quitter")
        quit_action.triggered.connect(self.close)

    def reset_application(self):
        """Réinitialise l'application en rechargeant les données depuis la base de données."""
        # Demander confirmation à l'utilisateur
        reply = QMessageBox.question(self, "Confirmation",
                                   "Voulez-vous vraiment réinitialiser l'application ? \n\nCela rechargera toutes les données depuis la base de données.",
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)

        if reply == QMessageBox.StandardButton.Yes:
            # Vider les tableaux
            self.group_campaign_table.model.removeRows(0, self.group_campaign_table.model.rowCount())
            self.campaigns_table.model.removeRows(0, self.campaigns_table.model.rowCount())

            # Réinitialiser les dropdowns
            self.account_widget.accounts_dropdown.clear()
            self.project_widget.projects_dropdown.clear()

            # Réinitialiser les filtres
            self.campaignsfilter.reset_filters()

            # Recharger les données initiales
            self.controller.load_initial_options()

            # Afficher un message de confirmation
            self.show_message("Réinitialisation", "L'application a été réinitialisée avec succès.")

    def return_to_main_menu(self):
        """Retourne au menu principal de sélection d'outils sans demander de confirmation."""
        # Importer ici pour éviter les imports circulaires
        import main

        # Créer et afficher la fenêtre de sélection d'outils
        self.tool_selection = main.ToolSelectionWindow(self.font_manager)
        self.tool_selection.show()

        # Fermer la fenêtre actuelle
        self.close()
