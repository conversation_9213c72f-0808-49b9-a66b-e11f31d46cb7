"""
Widget pour la gestion des comptes.
"""

from PyQt6.QtWidgets import QWidget, QVBoxLayout, QLabel, QComboBox, QPushButton, QHBoxLayout, QFrame
from PyQt6.QtCore import Qt
from PyQt6.QtGui import <PERSON>Pixmap, QPainter, QBrush

from .account_dialogs import AddAccountDialog, UserAccountDialog

def apply_rounded_mask(label, radius=10):
    """Applique un masque arrondi à un QLabel contenant une image."""
    size = label.size()
    mask = QPixmap(size)
    mask.fill(Qt.GlobalColor.transparent)

    painter = QPainter(mask)
    painter.setRenderHint(QPainter.RenderHint.Antialiasing)
    painter.setBrush(QBrush(Qt.GlobalColor.white))
    painter.setPen(Qt.PenStyle.NoPen)
    painter.drawRoundedRect(0, 0, size.width(), size.height(), radius, radius)
    painter.end()

    label.setMask(mask.createMaskFromColor(Qt.GlobalColor.transparent))

class AccountWidget(QWidget):
    def __init__(self, font_manager):
        super().__init__()

        self.font_manager = font_manager
        self.setFont(self.font_manager.get_font("regular", 12))

        self.frame = QFrame(self)
        self.frame.setObjectName("accountFrame")
        self.frame.setFixedSize(320, 100)  # Même taille que AccountWidget
        self.frame.setStyleSheet("""
            QFrame#accountFrame {
                border: 1px solid gray;
                border-radius: 10px;
            }
        """)

        # Layout principal pour l'ensemble des infos Account
        self.account_main_layout = QHBoxLayout(self.frame)  # ⬅️ PAS de self.setLayout() ici
        self.account_main_layout.setContentsMargins(10, 5, 10, 5)  # La méthode setContentsMargins(left, top, right, bottom
        self.account_main_layout.setSpacing(10)

        # Layout vertical pour les infos du compte (label + dropdown)
        account_info_layout = QVBoxLayout()
        # account_info_layout.setSpacing(0)
        account_info_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)  # Aligner à gauche

        # Label pour sélectionner un compte
        self.label1 = QLabel("Selected Account :")
        self.label1.setFont(self.font_manager.get_font("bold", 16))
        account_info_layout.addWidget(self.label1)

        # Dropdown pour choisir un compte
        self.accounts_dropdown = QComboBox()
        self.accounts_dropdown.setFixedSize(200, 40)
        account_info_layout.addWidget(self.accounts_dropdown)
        account_info_layout.addSpacing(0)

        account_button_layout = QHBoxLayout()
        self.add_account_button = QPushButton("Add Account")
        self.add_account_button.setFixedSize(100, self.add_account_button.sizeHint().height())
        self.user_account_button = QPushButton("View users")
        self.user_account_button.setFixedSize(100, self.user_account_button.sizeHint().height())

        account_button_layout.addWidget(self.add_account_button)
        account_button_layout.addWidget(self.user_account_button)

        # Ajouter ce layout vertical au layout horizontal
        account_info_layout.addLayout(account_button_layout)
        self.account_main_layout.addLayout(account_info_layout)

        # Ajout de l'image du compte (à droite)
        self.account_image = QLabel(self)
        self.account_image.setFixedSize(70, 70)
        apply_rounded_mask(self.account_image, radius=15)
        self.account_image.setScaledContents(True)

        # Ajouter l'image à droite
        self.account_main_layout.addWidget(self.account_image, alignment=Qt.AlignmentFlag.AlignRight)

    def get_layout(self):
        """Retourne le layout pour être ajouté ailleurs"""
        return self.frame

    def open_add_account_window(self, controller):
        """Ouvre une nouvelle fenêtre pour ajouter un projet"""
        dialog = AddAccountDialog(controller, self.font_manager)
        dialog.exec()  # Affiche la boîte de dialogue en mode bloquant

    def open_user_account_window(self, account_id, account_name, controller):
        """Ouvre une nouvelle fenêtre pour ajouter un projet"""
        dialog = UserAccountDialog(account_id, account_name, controller, self.font_manager)
        dialog.exec()  # Affiche la boîte de dialogue en mode bloquant
